﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "CMakePredefinedTargets", "CMakePredefinedTargets", "{2ABB827E-BF10-388F-A6FF-75CDEB083787}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "ExternalProjectTargets", "ExternalProjectTargets", "{16C984BD-F971-3A83-BD4D-AFCA7A6DA1D1}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "googletest-populate", "ExternalProjectTargets\googletest-populate", "{6F5AB336-EB37-3C62-8528-9B9D22B933B1}"
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ALL_BUILD", "ALL_BUILD.vcxproj", "{F377F81A-D29E-3A93-B168-712E58C6F7FC}"
	ProjectSection(ProjectDependencies) = postProject
		{AB707402-1239-396E-883A-B5BF5C6B982E} = {AB707402-1239-396E-883A-B5BF5C6B982E}
		{204C3879-DF20-317A-AE21-056E84D541E7} = {204C3879-DF20-317A-AE21-056E84D541E7}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ZERO_CHECK", "ZERO_CHECK.vcxproj", "{AB707402-1239-396E-883A-B5BF5C6B982E}"
	ProjectSection(ProjectDependencies) = postProject
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "googletest-populate", "googletest-populate.vcxproj", "{204C3879-DF20-317A-AE21-056E84D541E7}"
	ProjectSection(ProjectDependencies) = postProject
		{AB707402-1239-396E-883A-B5BF5C6B982E} = {AB707402-1239-396E-883A-B5BF5C6B982E}
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|x64 = Debug|x64
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{F377F81A-D29E-3A93-B168-712E58C6F7FC}.Debug|x64.ActiveCfg = Debug|x64
		{AB707402-1239-396E-883A-B5BF5C6B982E}.Debug|x64.ActiveCfg = Debug|x64
		{AB707402-1239-396E-883A-B5BF5C6B982E}.Debug|x64.Build.0 = Debug|x64
		{204C3879-DF20-317A-AE21-056E84D541E7}.Debug|x64.ActiveCfg = Debug|x64
		{204C3879-DF20-317A-AE21-056E84D541E7}.Debug|x64.Build.0 = Debug|x64
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{F377F81A-D29E-3A93-B168-712E58C6F7FC} = {2ABB827E-BF10-388F-A6FF-75CDEB083787}
		{AB707402-1239-396E-883A-B5BF5C6B982E} = {2ABB827E-BF10-388F-A6FF-75CDEB083787}
		{6F5AB336-EB37-3C62-8528-9B9D22B933B1} = {16C984BD-F971-3A83-BD4D-AFCA7A6DA1D1}
		{204C3879-DF20-317A-AE21-056E84D541E7} = {6F5AB336-EB37-3C62-8528-9B9D22B933B1}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {D7BB5B29-8EE1-3571-BEA7-CEE79E2B3AD3}
	EndGlobalSection
	GlobalSection(ExtensibilityAddIns) = postSolution
	EndGlobalSection
EndGlobal
