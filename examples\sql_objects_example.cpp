/**
 * @file sql_objects_example.cpp
 * @brief Comprehensive examples demonstrating usage of SQL object hierarchy
 *
 * This file provides practical examples of how to use SqlObject and all its subclasses:
 * - SqlObject (base class)
 * - SqlColumn (database columns)
 * - SqlIndex (database indexes)
 * - SqlRow (table rows)
 * - SqlTable (database tables)
 * - SqlView (database views)
 */

#include <iostream>
#include <vector>
#include <memory>
#include <string>

// Include all SQL object headers
#include "sql/sql_object.h"
#include "sql/sql_column.h"
#include "sql/sql_index.h"
#include "sql/sql_row.h"
#include "sql/sql_table.h"
#include "sql/sql_view.h"
#include "sql_database.h"
#include "sql_types.h"

using namespace database;

/**
 * @brief Example 1: Basic SqlColumn usage
 */
void demonstrateSqlColumn() {
    std::cout << "\n=== SqlColumn Examples ===\n";

    // Create columns with different data types and constraints
    SqlColumn idColumn("id", SqlDataType::Integer);
    idColumn.setConstraints(SqlColumnConstraint::PrimaryKey | SqlColumnConstraint::AutoIncrement);

    SqlColumn nameColumn("name", SqlDataType::Varchar);
    nameColumn.setLength(100);
    nameColumn.setConstraints(SqlColumnConstraint::NotNull);

    SqlColumn emailColumn("email", SqlDataType::Varchar);
    emailColumn.setLength(255);
    emailColumn.setConstraints(SqlColumnConstraint::Unique | SqlColumnConstraint::NotNull);

    SqlColumn ageColumn("age", SqlDataType::Integer);
    ageColumn.setDefaultValue(Data(0));

    SqlColumn createdAtColumn("created_at", SqlDataType::DateTime);
    createdAtColumn.setConstraints(SqlColumnConstraint::NotNull);

    // Display column information
    std::cout << "ID Column: " << idColumn.toSql() << std::endl;
    std::cout << "Name Column: " << nameColumn.toSql() << std::endl;
    std::cout << "Email Column: " << emailColumn.toSql() << std::endl;
    std::cout << "Age Column: " << ageColumn.toSql() << std::endl;
    std::cout << "Created At Column: " << createdAtColumn.toSql() << std::endl;

    // Demonstrate column properties
    std::cout << "\nColumn Properties:\n";
    std::cout << "ID Column is primary key: " << idColumn.isPrimaryKey() << std::endl;
    std::cout << "Email Column is unique: " << emailColumn.isUnique() << std::endl;
    std::cout << "Name Column allows null: " << !nameColumn.isNotNull() << std::endl;
}

/**
 * @brief Example 2: SqlIndex creation and management
 */
void demonstrateSqlIndex() {
    std::cout << "\n=== SqlIndex Examples ===\n";

    // Create different types of indexes
    SqlIndex primaryIndex("pk_users", SqlIndexType::Primary);
    primaryIndex.addColumn("id");

    SqlIndex uniqueEmailIndex("idx_unique_email", SqlIndexType::Unique);
    uniqueEmailIndex.addColumn("email");

    SqlIndex nameIndex("idx_name", SqlIndexType::Normal);
    nameIndex.addColumn("name", SqlSortOrder::Ascending);

    // Composite index
    SqlIndex compositeIndex("idx_name_age", SqlIndexType::Normal);
    compositeIndex.addColumn("name", SqlSortOrder::Ascending);
    compositeIndex.addColumn("age", SqlSortOrder::Descending);

    // Display index information
    std::cout << "Primary Index: " << primaryIndex.toSql() << std::endl;
    std::cout << "Unique Email Index: " << uniqueEmailIndex.toSql() << std::endl;
    std::cout << "Name Index: " << nameIndex.toSql() << std::endl;
    std::cout << "Composite Index: " << compositeIndex.toSql() << std::endl;

    // Demonstrate index properties
    std::cout << "\nIndex Properties:\n";
    std::cout << "Primary Index type: " << static_cast<int>(primaryIndex.indexType()) << std::endl;
    std::cout << "Composite Index column count: " << compositeIndex.columnCount() << std::endl;
    std::cout << "Name Index is unique: " << nameIndex.isUnique() << std::endl;
}

/**
 * @brief Example 3: SqlRow data manipulation
 */
void demonstrateSqlRow() {
    std::cout << "\n=== SqlRow Examples ===\n";

    // Create a row with data
    SqlRow userRow("user_row");

    // Set column values
    userRow.setValue("id", Data(1));
    userRow.setValue("name", Data(std::string("John Doe")));
    userRow.setValue("email", Data(std::string("<EMAIL>")));
    userRow.setValue("age", Data(30));

    // Display row information
    std::cout << "Row SQL: " << userRow.toSql() << std::endl;
    std::cout << "Row as JSON: " << userRow.toJson() << std::endl;

    // Access individual values
    std::cout << "\nRow Values:\n";
    if (auto id = userRow.value("id")) {
        std::cout << "ID: " << id->toString() << std::endl;
    }
    if (auto name = userRow.value("name")) {
        std::cout << "Name: " << name->toString() << std::endl;
    }
    if (auto email = userRow.value("email")) {
        std::cout << "Email: " << email->toString() << std::endl;
    }

    // Demonstrate row operations
    std::cout << "\nRow Operations:\n";
    std::cout << "Column count: " << userRow.columnCount() << std::endl;
    std::cout << "Has 'name' column: " << userRow.hasColumn("name") << std::endl;
    std::cout << "Has 'salary' column: " << userRow.hasColumn("salary") << std::endl;

    // Generate SQL statements
    std::cout << "\nGenerated SQL:\n";
    std::cout << "Insert: " << userRow.toInsertStatement("users") << std::endl;
    std::cout << "Update: " << userRow.toUpdateStatement("id = 1", "users") << std::endl;
}

/**
 * @brief Example 4: SqlTable creation and management
 */
void demonstrateSqlTable() {
    std::cout << "\n=== SqlTable Examples ===\n";

    // Create a table
    SqlTable usersTable("users");

    // Add columns to the table
    std::vector<SqlColumn> columns;

    SqlColumn idCol("id", SqlDataType::Integer);
    idCol.setConstraints(SqlColumnConstraint::PrimaryKey | SqlColumnConstraint::AutoIncrement);
    columns.push_back(idCol);

    SqlColumn nameCol("name", SqlDataType::Varchar);
    nameCol.setLength(100);
    nameCol.setConstraints(SqlColumnConstraint::NotNull);
    columns.push_back(nameCol);

    SqlColumn emailCol("email", SqlDataType::Varchar);
    emailCol.setLength(255);
    emailCol.setConstraints(SqlColumnConstraint::Unique | SqlColumnConstraint::NotNull);
    columns.push_back(emailCol);

    // Set table metadata
    if (auto metadata = usersTable.metadata()) {
        metadata->setColumns(columns);
        metadata->comment = "User information table";
        metadata->engine = "InnoDB";
    }

    // Display table information
    std::cout << "Table SQL: " << usersTable.toSql() << std::endl;
    std::cout << "Table qualified name: " << usersTable.qualifiedName() << std::endl;

    // Demonstrate table properties
    std::cout << "\nTable Properties:\n";
    if (auto metadata = usersTable.metadata()) {
        std::cout << "Column count: " << metadata->columns.size() << std::endl;
        std::cout << "Table comment: " << metadata->comment << std::endl;
        std::cout << "Storage engine: " << metadata->engine << std::endl;
    }
}

/**
 * @brief Example 5: SqlView creation and management
 */
void demonstrateSqlView() {
    std::cout << "\n=== SqlView Examples ===\n";

    // Create a view
    SqlView activeUsersView("active_users");

    // Set view query
    std::string viewQuery = R"(
        SELECT id, name, email
        FROM users
        WHERE created_at > DATE_SUB(NOW(), INTERVAL 30 DAY)
    )";

    activeUsersView.setQuery(viewQuery);

    // Set view metadata
    if (auto metadata = activeUsersView.metadata()) {
        metadata->comment = "View of users active in the last 30 days";
        metadata->isUpdatable = false;
        metadata->dependencies.push_back("users");
    }

    // Display view information
    std::cout << "View SQL: " << activeUsersView.toSql() << std::endl;
    std::cout << "View qualified name: " << activeUsersView.qualifiedName() << std::endl;

    // Demonstrate view properties
    std::cout << "\nView Properties:\n";
    if (auto metadata = activeUsersView.metadata()) {
        std::cout << "View comment: " << metadata->comment << std::endl;
        std::cout << "Is updatable: " << metadata->isUpdatable << std::endl;
        std::cout << "Dependencies: ";
        for (const auto& dep : metadata->dependencies) {
            std::cout << dep << " ";
        }
        std::cout << std::endl;
    }
}

/**
 * @brief Example 6: Advanced SQL object interactions
 */
void demonstrateAdvancedInteractions() {
    std::cout << "\n=== Advanced SQL Object Interactions ===\n";

    // Create a complete database schema
    SqlTable usersTable("users");
    SqlTable ordersTable("orders");
    SqlTable productsTable("products");

    // Set up users table
    std::vector<SqlColumn> userColumns;

    SqlColumn userIdCol("id", SqlDataType::Integer);
    userIdCol.setConstraints(SqlColumnConstraint::PrimaryKey | SqlColumnConstraint::AutoIncrement);
    userColumns.push_back(userIdCol);

    SqlColumn userNameCol("name", SqlDataType::Varchar);
    userNameCol.setLength(100);
    userNameCol.setConstraints(SqlColumnConstraint::NotNull);
    userColumns.push_back(userNameCol);

    SqlColumn userEmailCol("email", SqlDataType::Varchar);
    userEmailCol.setLength(255);
    userEmailCol.setConstraints(SqlColumnConstraint::Unique | SqlColumnConstraint::NotNull);
    userColumns.push_back(userEmailCol);

    if (auto metadata = usersTable.metadata()) {
        metadata->setColumns(userColumns);
    }

    // Set up orders table with foreign key
    std::vector<SqlColumn> orderColumns;

    SqlColumn orderIdCol("id", SqlDataType::Integer);
    orderIdCol.setConstraints(SqlColumnConstraint::PrimaryKey | SqlColumnConstraint::AutoIncrement);
    orderColumns.push_back(orderIdCol);

    SqlColumn orderUserIdCol("user_id", SqlDataType::Integer);
    orderUserIdCol.setConstraints(SqlColumnConstraint::ForeignKey | SqlColumnConstraint::NotNull);
    orderColumns.push_back(orderUserIdCol);

    SqlColumn orderTotalCol("total", SqlDataType::Decimal);
    orderTotalCol.setPrecisionScale(10, 2);
    orderTotalCol.setDefaultValue(Data(0.00));
    orderColumns.push_back(orderTotalCol);

    if (auto metadata = ordersTable.metadata()) {
        metadata->setColumns(orderColumns);
    }

    // Create indexes for performance
    SqlIndex userEmailIndex("idx_user_email", SqlIndexType::Unique);
    userEmailIndex.setTableName("users");
    userEmailIndex.addColumn("email");

    SqlIndex orderUserIndex("idx_order_user", SqlIndexType::Normal);
    orderUserIndex.setTableName("orders");
    orderUserIndex.addColumn("user_id");

    // Create a view that joins tables
    SqlView userOrdersView("user_orders_summary");
    std::string viewQuery = R"(
        SELECT
            u.id as user_id,
            u.name as user_name,
            u.email,
            COUNT(o.id) as order_count,
            COALESCE(SUM(o.total), 0) as total_spent
        FROM users u
        LEFT JOIN orders o ON u.id = o.user_id
        GROUP BY u.id, u.name, u.email
        ORDER BY total_spent DESC
    )";
    userOrdersView.setQuery(viewQuery);
    userOrdersView.addDependency("users");
    userOrdersView.addDependency("orders");

    // Create sample data rows
    SqlRow user1("user1");
    user1.setValue("id", Data(1));
    user1.setValue("name", Data(std::string("Alice Johnson")));
    user1.setValue("email", Data(std::string("<EMAIL>")));

    SqlRow user2("user2");
    user2.setValue("id", Data(2));
    user2.setValue("name", Data(std::string("Bob Smith")));
    user2.setValue("email", Data(std::string("<EMAIL>")));

    SqlRow order1("order1");
    order1.setValue("id", Data(1));
    order1.setValue("user_id", Data(1));
    order1.setValue("total", Data(99.99));

    SqlRow order2("order2");
    order2.setValue("id", Data(2));
    order2.setValue("user_id", Data(1));
    order2.setValue("total", Data(149.50));

    // Display the complete schema
    std::cout << "Complete Database Schema:\n";
    std::cout << "========================\n";
    std::cout << "Users Table: " << usersTable.toSql() << std::endl;
    std::cout << "Orders Table: " << ordersTable.toSql() << std::endl;
    std::cout << "User Email Index: " << userEmailIndex.toSql() << std::endl;
    std::cout << "Order User Index: " << orderUserIndex.toSql() << std::endl;
    std::cout << "User Orders View: " << userOrdersView.toSql() << std::endl;

    std::cout << "\nSample Data:\n";
    std::cout << "User 1 Insert: " << user1.toInsertStatement("users") << std::endl;
    std::cout << "User 2 Insert: " << user2.toInsertStatement("users") << std::endl;
    std::cout << "Order 1 Insert: " << order1.toInsertStatement("orders") << std::endl;
    std::cout << "Order 2 Insert: " << order2.toInsertStatement("orders") << std::endl;
}

/**
 * @brief Example 7: Error handling and edge cases
 */
void demonstrateErrorHandling() {
    std::cout << "\n=== Error Handling and Edge Cases ===\n";

    // Test invalid objects
    SqlColumn invalidColumn("", SqlDataType::Unknown);
    std::cout << "Invalid column is valid: " << invalidColumn.isValid() << std::endl;

    SqlTable invalidTable("");
    std::cout << "Invalid table is valid: " << invalidTable.isValid() << std::endl;

    // Test edge cases with data types
    SqlColumn largeVarchar("large_text", SqlDataType::Varchar);
    largeVarchar.setLength(65535);
    std::cout << "Large VARCHAR column: " << largeVarchar.toSql() << std::endl;

    SqlColumn precisionDecimal("precise_decimal", SqlDataType::Decimal);
    precisionDecimal.setPrecisionScale(65, 30);
    std::cout << "High precision decimal: " << precisionDecimal.toSql() << std::endl;

    // Test constraint combinations
    SqlColumn complexColumn("complex_col", SqlDataType::Integer);
    complexColumn.setConstraints(
        SqlColumnConstraint::NotNull |
        SqlColumnConstraint::Unique |
        SqlColumnConstraint::Check
    );
    std::cout << "Complex constraints: " << complexColumn.toSql() << std::endl;

    // Test row with null values
    SqlRow rowWithNulls("null_row");
    rowWithNulls.setValue("id", Data(1));
    rowWithNulls.setValue("name", Data(std::string("Test")));
    rowWithNulls.setValue("optional_field", Data()); // Null value
    std::cout << "Row with nulls JSON: " << rowWithNulls.toJson() << std::endl;

    // Test very long names
    std::string longName(100, 'x');
    SqlTable longNameTable(longName);
    std::cout << "Long name table is valid: " << longNameTable.isValid() << std::endl;
}

/**
 * @brief Main function demonstrating all SQL object examples
 */
int main() {
    std::cout << "SQL Object Hierarchy Examples\n";
    std::cout << "=============================\n";

    try {
        demonstrateSqlColumn();
        demonstrateSqlIndex();
        demonstrateSqlRow();
        demonstrateSqlTable();
        demonstrateSqlView();
        demonstrateAdvancedInteractions();
        demonstrateErrorHandling();

        std::cout << "\n=== All examples completed successfully! ===\n";

    } catch (const std::exception& e) {
        std::cerr << "Error: " << e.what() << std::endl;
        return 1;
    }

    return 0;
}
