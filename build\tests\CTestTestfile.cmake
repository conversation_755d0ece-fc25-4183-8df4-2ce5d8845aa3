# CMake generated Testfile for 
# Source directory: E:/Projects/Code/AI Agent/database_v13/tests
# Build directory: E:/Projects/Code/AI Agent/database_v13/build/tests
# 
# This file includes the relevant testing commands required for 
# testing this directory and lists subdirectories to be tested as well.
include("E:/Projects/Code/AI Agent/database_v13/build/tests/sql_objects_tests[1]_include.cmake")
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(SqlObjectTests "E:/Projects/Code/AI Agent/database_v13/build/bin/Debug/sql_objects_tests.exe" "--gtest_filter=\"SqlObjectTest.*\"")
  set_tests_properties(SqlObjectTests PROPERTIES  _BACKTRACE_TRIPLES "E:/Projects/Code/AI Agent/database_v13/tests/CMakeLists.txt;69;add_test;E:/Projects/Code/AI Agent/database_v13/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(SqlObjectTests "E:/Projects/Code/AI Agent/database_v13/build/bin/Release/sql_objects_tests.exe" "--gtest_filter=\"SqlObjectTest.*\"")
  set_tests_properties(SqlObjectTests PROPERTIES  _BACKTRACE_TRIPLES "E:/Projects/Code/AI Agent/database_v13/tests/CMakeLists.txt;69;add_test;E:/Projects/Code/AI Agent/database_v13/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Mm][Ii][Nn][Ss][Ii][Zz][Ee][Rr][Ee][Ll])$")
  add_test(SqlObjectTests "E:/Projects/Code/AI Agent/database_v13/build/bin/MinSizeRel/sql_objects_tests.exe" "--gtest_filter=\"SqlObjectTest.*\"")
  set_tests_properties(SqlObjectTests PROPERTIES  _BACKTRACE_TRIPLES "E:/Projects/Code/AI Agent/database_v13/tests/CMakeLists.txt;69;add_test;E:/Projects/Code/AI Agent/database_v13/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ww][Ii][Tt][Hh][Dd][Ee][Bb][Ii][Nn][Ff][Oo])$")
  add_test(SqlObjectTests "E:/Projects/Code/AI Agent/database_v13/build/bin/RelWithDebInfo/sql_objects_tests.exe" "--gtest_filter=\"SqlObjectTest.*\"")
  set_tests_properties(SqlObjectTests PROPERTIES  _BACKTRACE_TRIPLES "E:/Projects/Code/AI Agent/database_v13/tests/CMakeLists.txt;69;add_test;E:/Projects/Code/AI Agent/database_v13/tests/CMakeLists.txt;0;")
else()
  add_test(SqlObjectTests NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(SqlColumnTests "E:/Projects/Code/AI Agent/database_v13/build/bin/Debug/sql_objects_tests.exe" "--gtest_filter=\"SqlColumnTest.*\"")
  set_tests_properties(SqlColumnTests PROPERTIES  _BACKTRACE_TRIPLES "E:/Projects/Code/AI Agent/database_v13/tests/CMakeLists.txt;70;add_test;E:/Projects/Code/AI Agent/database_v13/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(SqlColumnTests "E:/Projects/Code/AI Agent/database_v13/build/bin/Release/sql_objects_tests.exe" "--gtest_filter=\"SqlColumnTest.*\"")
  set_tests_properties(SqlColumnTests PROPERTIES  _BACKTRACE_TRIPLES "E:/Projects/Code/AI Agent/database_v13/tests/CMakeLists.txt;70;add_test;E:/Projects/Code/AI Agent/database_v13/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Mm][Ii][Nn][Ss][Ii][Zz][Ee][Rr][Ee][Ll])$")
  add_test(SqlColumnTests "E:/Projects/Code/AI Agent/database_v13/build/bin/MinSizeRel/sql_objects_tests.exe" "--gtest_filter=\"SqlColumnTest.*\"")
  set_tests_properties(SqlColumnTests PROPERTIES  _BACKTRACE_TRIPLES "E:/Projects/Code/AI Agent/database_v13/tests/CMakeLists.txt;70;add_test;E:/Projects/Code/AI Agent/database_v13/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ww][Ii][Tt][Hh][Dd][Ee][Bb][Ii][Nn][Ff][Oo])$")
  add_test(SqlColumnTests "E:/Projects/Code/AI Agent/database_v13/build/bin/RelWithDebInfo/sql_objects_tests.exe" "--gtest_filter=\"SqlColumnTest.*\"")
  set_tests_properties(SqlColumnTests PROPERTIES  _BACKTRACE_TRIPLES "E:/Projects/Code/AI Agent/database_v13/tests/CMakeLists.txt;70;add_test;E:/Projects/Code/AI Agent/database_v13/tests/CMakeLists.txt;0;")
else()
  add_test(SqlColumnTests NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(SqlIndexTests "E:/Projects/Code/AI Agent/database_v13/build/bin/Debug/sql_objects_tests.exe" "--gtest_filter=\"SqlIndexTest.*\"")
  set_tests_properties(SqlIndexTests PROPERTIES  _BACKTRACE_TRIPLES "E:/Projects/Code/AI Agent/database_v13/tests/CMakeLists.txt;71;add_test;E:/Projects/Code/AI Agent/database_v13/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(SqlIndexTests "E:/Projects/Code/AI Agent/database_v13/build/bin/Release/sql_objects_tests.exe" "--gtest_filter=\"SqlIndexTest.*\"")
  set_tests_properties(SqlIndexTests PROPERTIES  _BACKTRACE_TRIPLES "E:/Projects/Code/AI Agent/database_v13/tests/CMakeLists.txt;71;add_test;E:/Projects/Code/AI Agent/database_v13/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Mm][Ii][Nn][Ss][Ii][Zz][Ee][Rr][Ee][Ll])$")
  add_test(SqlIndexTests "E:/Projects/Code/AI Agent/database_v13/build/bin/MinSizeRel/sql_objects_tests.exe" "--gtest_filter=\"SqlIndexTest.*\"")
  set_tests_properties(SqlIndexTests PROPERTIES  _BACKTRACE_TRIPLES "E:/Projects/Code/AI Agent/database_v13/tests/CMakeLists.txt;71;add_test;E:/Projects/Code/AI Agent/database_v13/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ww][Ii][Tt][Hh][Dd][Ee][Bb][Ii][Nn][Ff][Oo])$")
  add_test(SqlIndexTests "E:/Projects/Code/AI Agent/database_v13/build/bin/RelWithDebInfo/sql_objects_tests.exe" "--gtest_filter=\"SqlIndexTest.*\"")
  set_tests_properties(SqlIndexTests PROPERTIES  _BACKTRACE_TRIPLES "E:/Projects/Code/AI Agent/database_v13/tests/CMakeLists.txt;71;add_test;E:/Projects/Code/AI Agent/database_v13/tests/CMakeLists.txt;0;")
else()
  add_test(SqlIndexTests NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(SqlRowTests "E:/Projects/Code/AI Agent/database_v13/build/bin/Debug/sql_objects_tests.exe" "--gtest_filter=\"SqlRowTest.*\"")
  set_tests_properties(SqlRowTests PROPERTIES  _BACKTRACE_TRIPLES "E:/Projects/Code/AI Agent/database_v13/tests/CMakeLists.txt;72;add_test;E:/Projects/Code/AI Agent/database_v13/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(SqlRowTests "E:/Projects/Code/AI Agent/database_v13/build/bin/Release/sql_objects_tests.exe" "--gtest_filter=\"SqlRowTest.*\"")
  set_tests_properties(SqlRowTests PROPERTIES  _BACKTRACE_TRIPLES "E:/Projects/Code/AI Agent/database_v13/tests/CMakeLists.txt;72;add_test;E:/Projects/Code/AI Agent/database_v13/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Mm][Ii][Nn][Ss][Ii][Zz][Ee][Rr][Ee][Ll])$")
  add_test(SqlRowTests "E:/Projects/Code/AI Agent/database_v13/build/bin/MinSizeRel/sql_objects_tests.exe" "--gtest_filter=\"SqlRowTest.*\"")
  set_tests_properties(SqlRowTests PROPERTIES  _BACKTRACE_TRIPLES "E:/Projects/Code/AI Agent/database_v13/tests/CMakeLists.txt;72;add_test;E:/Projects/Code/AI Agent/database_v13/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ww][Ii][Tt][Hh][Dd][Ee][Bb][Ii][Nn][Ff][Oo])$")
  add_test(SqlRowTests "E:/Projects/Code/AI Agent/database_v13/build/bin/RelWithDebInfo/sql_objects_tests.exe" "--gtest_filter=\"SqlRowTest.*\"")
  set_tests_properties(SqlRowTests PROPERTIES  _BACKTRACE_TRIPLES "E:/Projects/Code/AI Agent/database_v13/tests/CMakeLists.txt;72;add_test;E:/Projects/Code/AI Agent/database_v13/tests/CMakeLists.txt;0;")
else()
  add_test(SqlRowTests NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(SqlTableTests "E:/Projects/Code/AI Agent/database_v13/build/bin/Debug/sql_objects_tests.exe" "--gtest_filter=\"SqlTableTest.*\"")
  set_tests_properties(SqlTableTests PROPERTIES  _BACKTRACE_TRIPLES "E:/Projects/Code/AI Agent/database_v13/tests/CMakeLists.txt;73;add_test;E:/Projects/Code/AI Agent/database_v13/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(SqlTableTests "E:/Projects/Code/AI Agent/database_v13/build/bin/Release/sql_objects_tests.exe" "--gtest_filter=\"SqlTableTest.*\"")
  set_tests_properties(SqlTableTests PROPERTIES  _BACKTRACE_TRIPLES "E:/Projects/Code/AI Agent/database_v13/tests/CMakeLists.txt;73;add_test;E:/Projects/Code/AI Agent/database_v13/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Mm][Ii][Nn][Ss][Ii][Zz][Ee][Rr][Ee][Ll])$")
  add_test(SqlTableTests "E:/Projects/Code/AI Agent/database_v13/build/bin/MinSizeRel/sql_objects_tests.exe" "--gtest_filter=\"SqlTableTest.*\"")
  set_tests_properties(SqlTableTests PROPERTIES  _BACKTRACE_TRIPLES "E:/Projects/Code/AI Agent/database_v13/tests/CMakeLists.txt;73;add_test;E:/Projects/Code/AI Agent/database_v13/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ww][Ii][Tt][Hh][Dd][Ee][Bb][Ii][Nn][Ff][Oo])$")
  add_test(SqlTableTests "E:/Projects/Code/AI Agent/database_v13/build/bin/RelWithDebInfo/sql_objects_tests.exe" "--gtest_filter=\"SqlTableTest.*\"")
  set_tests_properties(SqlTableTests PROPERTIES  _BACKTRACE_TRIPLES "E:/Projects/Code/AI Agent/database_v13/tests/CMakeLists.txt;73;add_test;E:/Projects/Code/AI Agent/database_v13/tests/CMakeLists.txt;0;")
else()
  add_test(SqlTableTests NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(SqlViewTests "E:/Projects/Code/AI Agent/database_v13/build/bin/Debug/sql_objects_tests.exe" "--gtest_filter=\"SqlViewTest.*\"")
  set_tests_properties(SqlViewTests PROPERTIES  _BACKTRACE_TRIPLES "E:/Projects/Code/AI Agent/database_v13/tests/CMakeLists.txt;74;add_test;E:/Projects/Code/AI Agent/database_v13/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(SqlViewTests "E:/Projects/Code/AI Agent/database_v13/build/bin/Release/sql_objects_tests.exe" "--gtest_filter=\"SqlViewTest.*\"")
  set_tests_properties(SqlViewTests PROPERTIES  _BACKTRACE_TRIPLES "E:/Projects/Code/AI Agent/database_v13/tests/CMakeLists.txt;74;add_test;E:/Projects/Code/AI Agent/database_v13/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Mm][Ii][Nn][Ss][Ii][Zz][Ee][Rr][Ee][Ll])$")
  add_test(SqlViewTests "E:/Projects/Code/AI Agent/database_v13/build/bin/MinSizeRel/sql_objects_tests.exe" "--gtest_filter=\"SqlViewTest.*\"")
  set_tests_properties(SqlViewTests PROPERTIES  _BACKTRACE_TRIPLES "E:/Projects/Code/AI Agent/database_v13/tests/CMakeLists.txt;74;add_test;E:/Projects/Code/AI Agent/database_v13/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ww][Ii][Tt][Hh][Dd][Ee][Bb][Ii][Nn][Ff][Oo])$")
  add_test(SqlViewTests "E:/Projects/Code/AI Agent/database_v13/build/bin/RelWithDebInfo/sql_objects_tests.exe" "--gtest_filter=\"SqlViewTest.*\"")
  set_tests_properties(SqlViewTests PROPERTIES  _BACKTRACE_TRIPLES "E:/Projects/Code/AI Agent/database_v13/tests/CMakeLists.txt;74;add_test;E:/Projects/Code/AI Agent/database_v13/tests/CMakeLists.txt;0;")
else()
  add_test(SqlViewTests NOT_AVAILABLE)
endif()
