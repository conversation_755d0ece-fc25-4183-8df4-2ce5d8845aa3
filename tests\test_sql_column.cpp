/**
 * @file test_sql_column.cpp
 * @brief Unit tests for SqlColumn class
 */

#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include "test_utils.h"
#include "sql/sql_column.h"

using namespace database;
using namespace database::test;
using ::testing::_;
using ::testing::Return;

/**
 * @brief Test fixture for SqlColumn tests
 */
class SqlColumnTest : public SqlObjectTestBase {
protected:
    void SetUp() override {
        SqlObjectTestBase::SetUp();
    }
};

/**
 * @brief Test SqlColumn default constructor
 */
TEST_F(SqlColumnTest, DefaultConstructor) {
    SqlColumn column;
    
    EXPECT_TRUE(column.name().empty());
    EXPECT_EQ(column.objectType(), SqlObjectType::Column);
    EXPECT_FALSE(column.isValid());
    EXPECT_EQ(column.dataType(), SqlDataType::Unknown);
}

/**
 * @brief Test SqlColumn constructor with name
 */
TEST_F(SqlColumnTest, ConstructorWithName) {
    const std::string columnName = "test_column";
    SqlColumn column(columnName);
    
    EXPECT_EQ(column.name(), columnName);
    EXPECT_EQ(column.objectType(), SqlObjectType::Column);
    EXPECT_TRUE(column.isValid());
    EXPECT_EQ(column.dataType(), SqlDataType::Unknown);
}

/**
 * @brief Test SqlColumn constructor with name and data type
 */
TEST_F(SqlColumnTest, ConstructorWithNameAndDataType) {
    const std::string columnName = "id";
    const SqlDataType dataType = SqlDataType::Integer;
    SqlColumn column(columnName, dataType);
    
    EXPECT_EQ(column.name(), columnName);
    EXPECT_EQ(column.objectType(), SqlObjectType::Column);
    EXPECT_TRUE(column.isValid());
    EXPECT_EQ(column.dataType(), dataType);
}

/**
 * @brief Test SqlColumn data type management
 */
TEST_F(SqlColumnTest, DataTypeManagement) {
    SqlColumn column("test_col");
    
    // Test setting different data types
    column.setDataType(SqlDataType::Varchar);
    EXPECT_EQ(column.dataType(), SqlDataType::Varchar);
    
    column.setDataType(SqlDataType::Integer);
    EXPECT_EQ(column.dataType(), SqlDataType::Integer);
    
    column.setDataType(SqlDataType::DateTime);
    EXPECT_EQ(column.dataType(), SqlDataType::DateTime);
}

/**
 * @brief Test SqlColumn length management
 */
TEST_F(SqlColumnTest, LengthManagement) {
    SqlColumn column("varchar_col", SqlDataType::Varchar);
    
    // Default length should be 0
    EXPECT_EQ(column.length(), 0);
    
    // Set length
    column.setLength(255);
    EXPECT_EQ(column.length(), 255);
    
    // Set different length
    column.setLength(100);
    EXPECT_EQ(column.length(), 100);
    
    // Test with zero length
    column.setLength(0);
    EXPECT_EQ(column.length(), 0);
}

/**
 * @brief Test SqlColumn precision and scale management
 */
TEST_F(SqlColumnTest, PrecisionScaleManagement) {
    SqlColumn column("decimal_col", SqlDataType::Decimal);
    
    // Default precision and scale
    EXPECT_EQ(column.precision(), 0);
    EXPECT_EQ(column.scale(), 0);
    
    // Set precision
    column.setPrecision(10);
    EXPECT_EQ(column.precision(), 10);
    
    // Set scale
    column.setScale(2);
    EXPECT_EQ(column.scale(), 2);
    
    // Set both together
    column.setPrecisionScale(15, 4);
    EXPECT_EQ(column.precision(), 15);
    EXPECT_EQ(column.scale(), 4);
}

/**
 * @brief Test SqlColumn constraint management
 */
TEST_F(SqlColumnTest, ConstraintManagement) {
    SqlColumn column("test_col", SqlDataType::Integer);
    
    // Default constraints
    EXPECT_EQ(column.constraints(), SqlColumnConstraint::None);
    EXPECT_FALSE(column.isNotNull());
    EXPECT_FALSE(column.isUnique());
    EXPECT_FALSE(column.isPrimaryKey());
    EXPECT_FALSE(column.isAutoIncrement());
    
    // Set NOT NULL constraint
    column.setConstraints(SqlColumnConstraint::NotNull);
    EXPECT_TRUE(column.isNotNull());
    EXPECT_FALSE(column.isUnique());
    
    // Add UNIQUE constraint
    column.setConstraints(SqlColumnConstraint::NotNull | SqlColumnConstraint::Unique);
    EXPECT_TRUE(column.isNotNull());
    EXPECT_TRUE(column.isUnique());
    
    // Set PRIMARY KEY (should imply NOT NULL and UNIQUE)
    column.setConstraints(SqlColumnConstraint::PrimaryKey);
    EXPECT_TRUE(column.isPrimaryKey());
    
    // Set AUTO_INCREMENT
    column.setConstraints(SqlColumnConstraint::AutoIncrement);
    EXPECT_TRUE(column.isAutoIncrement());
    
    // Combine multiple constraints
    column.setConstraints(
        SqlColumnConstraint::PrimaryKey | 
        SqlColumnConstraint::AutoIncrement
    );
    EXPECT_TRUE(column.isPrimaryKey());
    EXPECT_TRUE(column.isAutoIncrement());
}

/**
 * @brief Test SqlColumn default value management
 */
TEST_F(SqlColumnTest, DefaultValueManagement) {
    SqlColumn column("test_col", SqlDataType::Integer);
    
    // No default value initially
    EXPECT_FALSE(column.hasDefaultValue());
    EXPECT_FALSE(column.defaultValue().has_value());
    
    // Set default value
    Data defaultVal(42);
    column.setDefaultValue(defaultVal);
    EXPECT_TRUE(column.hasDefaultValue());
    EXPECT_TRUE(column.defaultValue().has_value());
    EXPECT_EQ(column.defaultValue()->toInt(), 42);
    
    // Clear default value
    column.clearDefaultValue();
    EXPECT_FALSE(column.hasDefaultValue());
    EXPECT_FALSE(column.defaultValue().has_value());
    
    // Set string default value
    column.setDefaultValue(Data(std::string("default_text")));
    EXPECT_TRUE(column.hasDefaultValue());
    EXPECT_EQ(column.defaultValue()->toString(), "default_text");
}

/**
 * @brief Test SqlColumn table name management
 */
TEST_F(SqlColumnTest, TableNameManagement) {
    SqlColumn column("test_col", SqlDataType::Varchar);
    
    // Default table name should be empty
    EXPECT_TRUE(column.tableName().empty());
    
    // Set table name
    column.setTableName("users");
    EXPECT_EQ(column.tableName(), "users");
    
    // Change table name
    column.setTableName("products");
    EXPECT_EQ(column.tableName(), "products");
}

/**
 * @brief Test SqlColumn comment management
 */
TEST_F(SqlColumnTest, CommentManagement) {
    SqlColumn column("test_col", SqlDataType::Text);
    
    // Default comment should be empty
    EXPECT_TRUE(column.comment().empty());
    
    // Set comment
    const std::string comment = "This is a test column";
    column.setComment(comment);
    EXPECT_EQ(column.comment(), comment);
    
    // Clear comment
    column.setComment("");
    EXPECT_TRUE(column.comment().empty());
}

/**
 * @brief Test SqlColumn SQL generation
 */
TEST_F(SqlColumnTest, SqlGeneration) {
    // Basic column
    SqlColumn basicCol("name", SqlDataType::Varchar);
    basicCol.setLength(100);
    std::string sql = basicCol.toSql();
    EXPECT_FALSE(sql.empty());
    EXPECT_NE(sql.find("name"), std::string::npos);
    EXPECT_NE(sql.find("VARCHAR"), std::string::npos);
    
    // Column with constraints
    SqlColumn constrainedCol("id", SqlDataType::Integer);
    constrainedCol.setConstraints(SqlColumnConstraint::PrimaryKey | SqlColumnConstraint::AutoIncrement);
    sql = constrainedCol.toSql();
    EXPECT_FALSE(sql.empty());
    EXPECT_NE(sql.find("id"), std::string::npos);
    EXPECT_NE(sql.find("INTEGER"), std::string::npos);
    
    // Column with default value
    SqlColumn defaultCol("status", SqlDataType::Integer);
    defaultCol.setDefaultValue(Data(1));
    sql = defaultCol.toSql();
    EXPECT_FALSE(sql.empty());
    EXPECT_NE(sql.find("status"), std::string::npos);
}

/**
 * @brief Test SqlColumn qualified name
 */
TEST_F(SqlColumnTest, QualifiedName) {
    SqlColumn column("test_col", SqlDataType::Integer);
    
    // Without table name
    std::string qualifiedName = column.qualifiedName();
    EXPECT_EQ(qualifiedName, "test_col");
    
    // With table name
    column.setTableName("test_table");
    qualifiedName = column.qualifiedName();
    EXPECT_EQ(qualifiedName, "test_table.test_col");
}

/**
 * @brief Test SqlColumn copy and move semantics
 */
TEST_F(SqlColumnTest, CopyMoveSemantics) {
    SqlColumn original("original_col", SqlDataType::Varchar);
    original.setLength(255);
    original.setConstraints(SqlColumnConstraint::NotNull | SqlColumnConstraint::Unique);
    original.setDefaultValue(Data(std::string("default")));
    original.setTableName("test_table");
    original.setComment("Test column");
    
    // Copy constructor
    SqlColumn copied(original);
    EXPECT_EQ(copied.name(), original.name());
    EXPECT_EQ(copied.dataType(), original.dataType());
    EXPECT_EQ(copied.length(), original.length());
    EXPECT_EQ(copied.constraints(), original.constraints());
    EXPECT_EQ(copied.tableName(), original.tableName());
    EXPECT_EQ(copied.comment(), original.comment());
    EXPECT_EQ(copied.defaultValue()->toString(), original.defaultValue()->toString());
    
    // Move constructor
    SqlColumn moved(std::move(original));
    EXPECT_EQ(moved.name(), "original_col");
    EXPECT_EQ(moved.dataType(), SqlDataType::Varchar);
    EXPECT_EQ(moved.length(), 255);
    EXPECT_EQ(moved.tableName(), "test_table");
}

/**
 * @brief Test SqlColumn equality and comparison
 */
TEST_F(SqlColumnTest, EqualityComparison) {
    SqlColumn col1("test_col", SqlDataType::Integer);
    SqlColumn col2("test_col", SqlDataType::Integer);
    SqlColumn col3("different_col", SqlDataType::Integer);
    SqlColumn col4("test_col", SqlDataType::Varchar);
    
    // Equal columns
    EXPECT_TRUE(col1 == col2);
    EXPECT_FALSE(col1 != col2);
    
    // Different names
    EXPECT_FALSE(col1 == col3);
    EXPECT_TRUE(col1 != col3);
    
    // Different data types
    EXPECT_FALSE(col1 == col4);
    EXPECT_TRUE(col1 != col4);
}

/**
 * @brief Parameterized test for different data types
 */
class SqlColumnDataTypeTest : public SqlColumnTest,
                             public ::testing::WithParamInterface<SqlDataTypeTestParam> {
};

TEST_P(SqlColumnDataTypeTest, DataTypeHandling) {
    auto param = GetParam();
    SqlColumn column("test_col", param.dataType);
    
    EXPECT_EQ(column.dataType(), param.dataType);
    
    std::string sql = column.toSql();
    EXPECT_FALSE(sql.empty());
    
    // Check if the SQL contains the expected type string
    std::string upperSql = sql;
    std::transform(upperSql.begin(), upperSql.end(), upperSql.begin(), ::toupper);
    EXPECT_NE(upperSql.find(param.expectedString), std::string::npos);
}

INSTANTIATE_TEST_SUITE_P(
    AllDataTypes,
    SqlColumnDataTypeTest,
    ::testing::ValuesIn(TestDataSets::getAllDataTypes())
);

/**
 * @brief Parameterized test for different constraints
 */
class SqlColumnConstraintTest : public SqlColumnTest,
                               public ::testing::WithParamInterface<SqlConstraintTestParam> {
};

TEST_P(SqlColumnConstraintTest, ConstraintHandling) {
    auto param = GetParam();
    SqlColumn column("test_col", SqlDataType::Integer);
    column.setConstraints(param.constraint);
    
    EXPECT_TRUE(hasConstraint(column.constraints(), param.constraint));
    
    // Test specific constraint checks
    switch (param.constraint) {
        case SqlColumnConstraint::NotNull:
            EXPECT_TRUE(column.isNotNull());
            break;
        case SqlColumnConstraint::Unique:
            EXPECT_TRUE(column.isUnique());
            break;
        case SqlColumnConstraint::PrimaryKey:
            EXPECT_TRUE(column.isPrimaryKey());
            break;
        case SqlColumnConstraint::AutoIncrement:
            EXPECT_TRUE(column.isAutoIncrement());
            break;
        default:
            break;
    }
}

INSTANTIATE_TEST_SUITE_P(
    AllConstraints,
    SqlColumnConstraintTest,
    ::testing::ValuesIn(TestDataSets::getAllConstraints())
);
