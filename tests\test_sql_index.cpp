/**
 * @file test_sql_index.cpp
 * @brief Unit tests for SqlIndex class
 */

#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include "test_utils.h"
#include "sql/sql_index.h"
#include "sql/sql_table.h"

using namespace database;
using namespace database::test;
using ::testing::_;
using ::testing::Return;

/**
 * @brief Test fixture for SqlIndex tests
 */
class SqlIndexTest : public SqlObjectTestBase {
protected:
    void SetUp() override {
        SqlObjectTestBase::SetUp();
        testTable = TestDataGenerator::createTestTable("test_table");
    }
    
    SqlTable testTable;
};

/**
 * @brief Test SqlIndex default constructor
 */
TEST_F(SqlIndexTest, DefaultConstructor) {
    SqlIndex index;
    
    EXPECT_TRUE(index.name().empty());
    EXPECT_EQ(index.objectType(), SqlObjectType::Index);
    EXPECT_FALSE(index.isValid());
    EXPECT_EQ(index.indexType(), SqlIndexType::Unknown);
}

/**
 * @brief Test SqlIndex constructor with name
 */
TEST_F(SqlIndexTest, ConstructorWithName) {
    const std::string indexName = "test_index";
    SqlIndex index(indexName);
    
    EXPECT_EQ(index.name(), indexName);
    EXPECT_EQ(index.objectType(), SqlObjectType::Index);
    EXPECT_TRUE(index.isValid());
    EXPECT_EQ(index.indexType(), SqlIndexType::Unknown);
}

/**
 * @brief Test SqlIndex constructor with name and type
 */
TEST_F(SqlIndexTest, ConstructorWithNameAndType) {
    const std::string indexName = "unique_index";
    const SqlIndexType indexType = SqlIndexType::Unique;
    SqlIndex index(indexName, indexType);
    
    EXPECT_EQ(index.name(), indexName);
    EXPECT_EQ(index.objectType(), SqlObjectType::Index);
    EXPECT_TRUE(index.isValid());
    EXPECT_EQ(index.indexType(), indexType);
}

/**
 * @brief Test SqlIndex constructor with table
 */
TEST_F(SqlIndexTest, ConstructorWithTable) {
    const std::string indexName = "table_index";
    SqlIndex index(indexName, testTable);
    
    EXPECT_EQ(index.name(), indexName);
    EXPECT_EQ(index.objectType(), SqlObjectType::Index);
    EXPECT_TRUE(index.isValid());
    EXPECT_EQ(index.tableName(), testTable.name());
}

/**
 * @brief Test SqlIndex type management
 */
TEST_F(SqlIndexTest, IndexTypeManagement) {
    SqlIndex index("test_index");
    
    // Test setting different index types
    index.setIndexType(SqlIndexType::Primary);
    EXPECT_EQ(index.indexType(), SqlIndexType::Primary);
    EXPECT_TRUE(index.isPrimary());
    EXPECT_TRUE(index.isUnique()); // Primary implies unique
    
    index.setIndexType(SqlIndexType::Unique);
    EXPECT_EQ(index.indexType(), SqlIndexType::Unique);
    EXPECT_FALSE(index.isPrimary());
    EXPECT_TRUE(index.isUnique());
    
    index.setIndexType(SqlIndexType::Normal);
    EXPECT_EQ(index.indexType(), SqlIndexType::Normal);
    EXPECT_FALSE(index.isPrimary());
    EXPECT_FALSE(index.isUnique());
    
    index.setIndexType(SqlIndexType::Hash);
    EXPECT_EQ(index.indexType(), SqlIndexType::Hash);
    
    index.setIndexType(SqlIndexType::FullText);
    EXPECT_EQ(index.indexType(), SqlIndexType::FullText);
}

/**
 * @brief Test SqlIndex column management
 */
TEST_F(SqlIndexTest, ColumnManagement) {
    SqlIndex index("multi_column_index");
    
    // Initially no columns
    EXPECT_EQ(index.columnCount(), 0);
    EXPECT_TRUE(index.columns().empty());
    EXPECT_FALSE(index.hasColumn("name"));
    
    // Add single column
    index.addColumn("name");
    EXPECT_EQ(index.columnCount(), 1);
    EXPECT_TRUE(index.hasColumn("name"));
    EXPECT_EQ(index.columns().size(), 1);
    EXPECT_EQ(index.columns()[0], "name");
    
    // Add column with sort order
    index.addColumn("age", SqlSortOrder::Descending);
    EXPECT_EQ(index.columnCount(), 2);
    EXPECT_TRUE(index.hasColumn("age"));
    
    // Add multiple columns
    std::vector<std::string> newColumns = {"email", "created_at"};
    index.addColumns(newColumns);
    EXPECT_EQ(index.columnCount(), 4);
    EXPECT_TRUE(index.hasColumn("email"));
    EXPECT_TRUE(index.hasColumn("created_at"));
    
    // Remove column
    index.removeColumn("age");
    EXPECT_EQ(index.columnCount(), 3);
    EXPECT_FALSE(index.hasColumn("age"));
    
    // Clear all columns
    index.clearColumns();
    EXPECT_EQ(index.columnCount(), 0);
    EXPECT_TRUE(index.columns().empty());
}

/**
 * @brief Test SqlIndex sort order management
 */
TEST_F(SqlIndexTest, SortOrderManagement) {
    SqlIndex index("sorted_index");
    
    // Add columns with different sort orders
    index.addColumn("name", SqlSortOrder::Ascending);
    index.addColumn("age", SqlSortOrder::Descending);
    index.addColumn("email"); // Default should be ascending
    
    auto sortOrders = index.sortOrders();
    EXPECT_EQ(sortOrders.size(), 3);
    EXPECT_EQ(sortOrders[0], SqlSortOrder::Ascending);
    EXPECT_EQ(sortOrders[1], SqlSortOrder::Descending);
    EXPECT_EQ(sortOrders[2], SqlSortOrder::Ascending); // Default
    
    // Set sort orders for all columns
    std::vector<SqlSortOrder> newOrders = {
        SqlSortOrder::Descending,
        SqlSortOrder::Ascending,
        SqlSortOrder::Descending
    };
    index.setSortOrders(newOrders);
    
    sortOrders = index.sortOrders();
    EXPECT_EQ(sortOrders.size(), 3);
    EXPECT_EQ(sortOrders[0], SqlSortOrder::Descending);
    EXPECT_EQ(sortOrders[1], SqlSortOrder::Ascending);
    EXPECT_EQ(sortOrders[2], SqlSortOrder::Descending);
}

/**
 * @brief Test SqlIndex table name management
 */
TEST_F(SqlIndexTest, TableNameManagement) {
    SqlIndex index("test_index");
    
    // Default table name should be empty
    EXPECT_TRUE(index.tableName().empty());
    
    // Set table name
    index.setTableName("users");
    EXPECT_EQ(index.tableName(), "users");
    
    // Change table name
    index.setTableName("products");
    EXPECT_EQ(index.tableName(), "products");
}

/**
 * @brief Test SqlIndex comment management
 */
TEST_F(SqlIndexTest, CommentManagement) {
    SqlIndex index("commented_index");
    
    // Default comment should be empty
    EXPECT_TRUE(index.comment().empty());
    
    // Set comment
    const std::string comment = "This is a test index for performance";
    index.setComment(comment);
    EXPECT_EQ(index.comment(), comment);
    
    // Clear comment
    index.setComment("");
    EXPECT_TRUE(index.comment().empty());
}

/**
 * @brief Test SqlIndex SQL generation
 */
TEST_F(SqlIndexTest, SqlGeneration) {
    // Basic index
    SqlIndex basicIndex("idx_name", SqlIndexType::Normal);
    basicIndex.setTableName("users");
    basicIndex.addColumn("name");
    
    std::string sql = basicIndex.toSql();
    EXPECT_FALSE(sql.empty());
    EXPECT_NE(sql.find("idx_name"), std::string::npos);
    EXPECT_NE(sql.find("users"), std::string::npos);
    EXPECT_NE(sql.find("name"), std::string::npos);
    
    // Unique index
    SqlIndex uniqueIndex("idx_unique_email", SqlIndexType::Unique);
    uniqueIndex.setTableName("users");
    uniqueIndex.addColumn("email");
    
    sql = uniqueIndex.toSql();
    EXPECT_FALSE(sql.empty());
    EXPECT_NE(sql.find("UNIQUE"), std::string::npos);
    
    // Multi-column index
    SqlIndex multiIndex("idx_multi", SqlIndexType::Normal);
    multiIndex.setTableName("users");
    multiIndex.addColumn("name", SqlSortOrder::Ascending);
    multiIndex.addColumn("age", SqlSortOrder::Descending);
    
    sql = multiIndex.toSql();
    EXPECT_FALSE(sql.empty());
    EXPECT_NE(sql.find("name"), std::string::npos);
    EXPECT_NE(sql.find("age"), std::string::npos);
}

/**
 * @brief Test SqlIndex qualified name
 */
TEST_F(SqlIndexTest, QualifiedName) {
    SqlIndex index("test_index");
    
    // Without table name
    std::string qualifiedName = index.qualifiedName();
    EXPECT_EQ(qualifiedName, "test_index");
    
    // With table name
    index.setTableName("test_table");
    qualifiedName = index.qualifiedName();
    EXPECT_EQ(qualifiedName, "test_table.test_index");
}

/**
 * @brief Test SqlIndex factory methods
 */
TEST_F(SqlIndexTest, FactoryMethods) {
    // Create primary key index
    auto primaryIndex = SqlIndex::createPrimaryKey("pk_users", "users", {"id"});
    EXPECT_EQ(primaryIndex.indexType(), SqlIndexType::Primary);
    EXPECT_EQ(primaryIndex.tableName(), "users");
    EXPECT_TRUE(primaryIndex.hasColumn("id"));
    EXPECT_TRUE(primaryIndex.isPrimary());
    
    // Create unique index
    auto uniqueIndex = SqlIndex::createUnique("uk_email", "users", {"email"});
    EXPECT_EQ(uniqueIndex.indexType(), SqlIndexType::Unique);
    EXPECT_EQ(uniqueIndex.tableName(), "users");
    EXPECT_TRUE(uniqueIndex.hasColumn("email"));
    EXPECT_TRUE(uniqueIndex.isUnique());
    
    // Create normal index
    auto normalIndex = SqlIndex::createIndex("idx_name", "users", {"name"});
    EXPECT_EQ(normalIndex.indexType(), SqlIndexType::Normal);
    EXPECT_EQ(normalIndex.tableName(), "users");
    EXPECT_TRUE(normalIndex.hasColumn("name"));
    EXPECT_FALSE(normalIndex.isUnique());
}

/**
 * @brief Test SqlIndex copy and move semantics
 */
TEST_F(SqlIndexTest, CopyMoveSemantics) {
    SqlIndex original("original_index", SqlIndexType::Unique);
    original.setTableName("test_table");
    original.addColumn("name", SqlSortOrder::Ascending);
    original.addColumn("age", SqlSortOrder::Descending);
    original.setComment("Original index comment");
    
    // Copy constructor
    SqlIndex copied(original);
    EXPECT_EQ(copied.name(), original.name());
    EXPECT_EQ(copied.indexType(), original.indexType());
    EXPECT_EQ(copied.tableName(), original.tableName());
    EXPECT_EQ(copied.columnCount(), original.columnCount());
    EXPECT_EQ(copied.comment(), original.comment());
    
    // Move constructor
    SqlIndex moved(std::move(original));
    EXPECT_EQ(moved.name(), "original_index");
    EXPECT_EQ(moved.indexType(), SqlIndexType::Unique);
    EXPECT_EQ(moved.tableName(), "test_table");
    EXPECT_EQ(moved.columnCount(), 2);
}

/**
 * @brief Test SqlIndex equality and comparison
 */
TEST_F(SqlIndexTest, EqualityComparison) {
    SqlIndex index1("test_index", SqlIndexType::Normal);
    SqlIndex index2("test_index", SqlIndexType::Normal);
    SqlIndex index3("different_index", SqlIndexType::Normal);
    SqlIndex index4("test_index", SqlIndexType::Unique);
    
    // Equal indexes
    EXPECT_TRUE(index1 == index2);
    EXPECT_FALSE(index1 != index2);
    
    // Different names
    EXPECT_FALSE(index1 == index3);
    EXPECT_TRUE(index1 != index3);
    
    // Different types
    EXPECT_FALSE(index1 == index4);
    EXPECT_TRUE(index1 != index4);
}

/**
 * @brief Parameterized test for different index types
 */
class SqlIndexTypeTest : public SqlIndexTest,
                        public ::testing::WithParamInterface<SqlIndexTypeTestParam> {
};

TEST_P(SqlIndexTypeTest, IndexTypeHandling) {
    auto param = GetParam();
    SqlIndex index("test_index", param.indexType);
    
    EXPECT_EQ(index.indexType(), param.indexType);
    EXPECT_EQ(index.isUnique(), param.isUnique);
    
    // Set table and column for SQL generation
    index.setTableName("test_table");
    index.addColumn("test_column");
    
    std::string sql = index.toSql();
    EXPECT_FALSE(sql.empty());
}

INSTANTIATE_TEST_SUITE_P(
    AllIndexTypes,
    SqlIndexTypeTest,
    ::testing::ValuesIn(TestDataSets::getAllIndexTypes())
);

/**
 * @brief Test SqlIndex hash functionality
 */
TEST_F(SqlIndexTest, HashFunctionality) {
    SqlIndex index1("test_index", SqlIndexType::Normal);
    index1.setTableName("users");
    index1.addColumn("name");
    
    SqlIndex index2("test_index", SqlIndexType::Normal);
    index2.setTableName("users");
    index2.addColumn("name");
    
    SqlIndex index3("different_index", SqlIndexType::Normal);
    index3.setTableName("users");
    index3.addColumn("name");
    
    // Test hash function
    std::hash<SqlIndex> hasher;
    size_t hash1 = hasher(index1);
    size_t hash2 = hasher(index2);
    size_t hash3 = hasher(index3);
    
    // Equal indexes should have equal hashes
    EXPECT_EQ(hash1, hash2);
    
    // Different indexes should likely have different hashes
    EXPECT_NE(hash1, hash3);
}
