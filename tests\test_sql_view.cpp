/**
 * @file test_sql_view.cpp
 * @brief Unit tests for SqlView class
 */

#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include "test_utils.h"
#include "sql/sql_view.h"

using namespace database;
using namespace database::test;
using ::testing::_;
using ::testing::Return;

/**
 * @brief Test fixture for SqlView tests
 */
class SqlViewTest : public SqlObjectTestBase {
protected:
    void SetUp() override {
        SqlObjectTestBase::SetUp();
    }
    
    std::string createTestQuery() {
        return R"(
            SELECT u.id, u.name, u.email, u.created_at
            FROM users u
            WHERE u.active = 1
            ORDER BY u.created_at DESC
        )";
    }
    
    std::string createComplexQuery() {
        return R"(
            SELECT 
                u.id,
                u.name,
                u.email,
                COUNT(o.id) as order_count,
                SUM(o.total) as total_spent
            FROM users u
            LEFT JOIN orders o ON u.id = o.user_id
            WHERE u.active = 1
            GROUP BY u.id, u.name, u.email
            HAVING COUNT(o.id) > 0
            ORDER BY total_spent DESC
        )";
    }
};

/**
 * @brief Test SqlView default constructor
 */
TEST_F(SqlViewTest, DefaultConstructor) {
    SqlView view;
    
    EXPECT_TRUE(view.name().empty());
    EXPECT_EQ(view.objectType(), SqlObjectType::View);
    EXPECT_FALSE(view.isValid());
    EXPECT_TRUE(view.query().empty());
}

/**
 * @brief Test SqlView constructor with name
 */
TEST_F(SqlViewTest, ConstructorWithName) {
    const std::string viewName = "active_users";
    SqlView view(viewName);
    
    EXPECT_EQ(view.name(), viewName);
    EXPECT_EQ(view.objectType(), SqlObjectType::View);
    EXPECT_TRUE(view.isValid());
    EXPECT_TRUE(view.query().empty());
}

/**
 * @brief Test SqlView constructor with name and query
 */
TEST_F(SqlViewTest, ConstructorWithNameAndQuery) {
    const std::string viewName = "active_users";
    const std::string query = createTestQuery();
    SqlView view(viewName, query);
    
    EXPECT_EQ(view.name(), viewName);
    EXPECT_EQ(view.objectType(), SqlObjectType::View);
    EXPECT_TRUE(view.isValid());
    EXPECT_EQ(view.query(), query);
}

/**
 * @brief Test SqlView query management
 */
TEST_F(SqlViewTest, QueryManagement) {
    SqlView view("test_view");
    
    // Initially empty query
    EXPECT_TRUE(view.query().empty());
    
    // Set query
    std::string testQuery = createTestQuery();
    view.setQuery(testQuery);
    EXPECT_EQ(view.query(), testQuery);
    
    // Change query
    std::string complexQuery = createComplexQuery();
    view.setQuery(complexQuery);
    EXPECT_EQ(view.query(), complexQuery);
    
    // Clear query
    view.setQuery("");
    EXPECT_TRUE(view.query().empty());
}

/**
 * @brief Test SqlView metadata management
 */
TEST_F(SqlViewTest, MetadataManagement) {
    SqlView view("test_view");
    
    // Get metadata (should be created automatically)
    auto metadata = view.metadata();
    EXPECT_TRUE(metadata != nullptr);
    
    // Set metadata properties
    metadata->schema = "public";
    metadata->comment = "Test view for active users";
    metadata->isUpdatable = false;
    metadata->checkOption = "CASCADED";
    metadata->algorithm = "MERGE";
    metadata->definer = "root@localhost";
    metadata->sqlSecurity = "DEFINER";
    
    // Add dependencies
    metadata->dependencies.push_back("users");
    metadata->dependencies.push_back("orders");
    
    // Verify metadata
    EXPECT_EQ(metadata->schema, "public");
    EXPECT_EQ(metadata->comment, "Test view for active users");
    EXPECT_FALSE(metadata->isUpdatable);
    EXPECT_EQ(metadata->checkOption, "CASCADED");
    EXPECT_EQ(metadata->algorithm, "MERGE");
    EXPECT_EQ(metadata->definer, "root@localhost");
    EXPECT_EQ(metadata->sqlSecurity, "DEFINER");
    EXPECT_EQ(metadata->dependencies.size(), 2);
    EXPECT_TRUE(std::find(metadata->dependencies.begin(), metadata->dependencies.end(), "users") != metadata->dependencies.end());
    EXPECT_TRUE(std::find(metadata->dependencies.begin(), metadata->dependencies.end(), "orders") != metadata->dependencies.end());
}

/**
 * @brief Test SqlView schema management
 */
TEST_F(SqlViewTest, SchemaManagement) {
    SqlView view("test_view");
    
    // Default schema should be empty
    EXPECT_TRUE(view.schema().empty());
    
    // Set schema
    view.setSchema("public");
    EXPECT_EQ(view.schema(), "public");
    
    // Change schema
    view.setSchema("analytics");
    EXPECT_EQ(view.schema(), "analytics");
}

/**
 * @brief Test SqlView dependency management
 */
TEST_F(SqlViewTest, DependencyManagement) {
    SqlView view("user_orders_view");
    
    // Initially no dependencies
    auto dependencies = view.dependencies();
    EXPECT_TRUE(dependencies.empty());
    
    // Add dependencies
    view.addDependency("users");
    view.addDependency("orders");
    view.addDependency("products");
    
    dependencies = view.dependencies();
    EXPECT_EQ(dependencies.size(), 3);
    EXPECT_TRUE(view.hasDependency("users"));
    EXPECT_TRUE(view.hasDependency("orders"));
    EXPECT_TRUE(view.hasDependency("products"));
    EXPECT_FALSE(view.hasDependency("categories"));
    
    // Remove dependency
    view.removeDependency("products");
    dependencies = view.dependencies();
    EXPECT_EQ(dependencies.size(), 2);
    EXPECT_FALSE(view.hasDependency("products"));
    
    // Clear all dependencies
    view.clearDependencies();
    dependencies = view.dependencies();
    EXPECT_TRUE(dependencies.empty());
}

/**
 * @brief Test SqlView updatable property
 */
TEST_F(SqlViewTest, UpdatableProperty) {
    SqlView view("test_view");
    
    // Default should be updatable (true)
    EXPECT_TRUE(view.isUpdatable());
    
    // Set not updatable
    view.setUpdatable(false);
    EXPECT_FALSE(view.isUpdatable());
    
    // Set updatable again
    view.setUpdatable(true);
    EXPECT_TRUE(view.isUpdatable());
}

/**
 * @brief Test SqlView comment management
 */
TEST_F(SqlViewTest, CommentManagement) {
    SqlView view("commented_view");
    
    // Default comment should be empty
    EXPECT_TRUE(view.comment().empty());
    
    // Set comment
    const std::string comment = "This view shows active users with their order statistics";
    view.setComment(comment);
    EXPECT_EQ(view.comment(), comment);
    
    // Clear comment
    view.setComment("");
    EXPECT_TRUE(view.comment().empty());
}

/**
 * @brief Test SqlView SQL generation
 */
TEST_F(SqlViewTest, SqlGeneration) {
    SqlView view("active_users_view");
    std::string testQuery = createTestQuery();
    view.setQuery(testQuery);
    view.setSchema("public");
    view.setComment("View of active users");
    
    // Test basic SQL generation
    std::string sql = view.toSql();
    EXPECT_FALSE(sql.empty());
    EXPECT_NE(sql.find("active_users_view"), std::string::npos);
    
    // Test CREATE VIEW SQL generation
    std::string createSql = view.createSql();
    EXPECT_FALSE(createSql.empty());
    EXPECT_NE(createSql.find("CREATE VIEW"), std::string::npos);
    EXPECT_NE(createSql.find("active_users_view"), std::string::npos);
    EXPECT_NE(createSql.find("SELECT"), std::string::npos);
    EXPECT_NE(createSql.find("FROM users"), std::string::npos);
    
    // Test DROP VIEW SQL generation
    std::string dropSql = view.dropSql();
    EXPECT_FALSE(dropSql.empty());
    EXPECT_NE(dropSql.find("DROP VIEW"), std::string::npos);
    EXPECT_NE(dropSql.find("active_users_view"), std::string::npos);
}

/**
 * @brief Test SqlView qualified name
 */
TEST_F(SqlViewTest, QualifiedName) {
    SqlView view("test_view");
    
    // Without schema
    std::string qualifiedName = view.qualifiedName();
    EXPECT_EQ(qualifiedName, "test_view");
    
    // With schema
    view.setSchema("analytics");
    qualifiedName = view.qualifiedName();
    EXPECT_EQ(qualifiedName, "analytics.test_view");
}

/**
 * @brief Test SqlView with complex query
 */
TEST_F(SqlViewTest, ComplexQuery) {
    SqlView view("user_order_summary");
    std::string complexQuery = createComplexQuery();
    
    view.setQuery(complexQuery);
    view.setSchema("reporting");
    view.setComment("Summary of user orders with totals");
    view.setUpdatable(false);
    
    // Add dependencies
    view.addDependency("users");
    view.addDependency("orders");
    
    // Verify complex view properties
    EXPECT_EQ(view.query(), complexQuery);
    EXPECT_EQ(view.schema(), "reporting");
    EXPECT_FALSE(view.isUpdatable());
    EXPECT_EQ(view.dependencies().size(), 2);
    
    // Test SQL generation with complex query
    std::string createSql = view.createSql();
    EXPECT_FALSE(createSql.empty());
    EXPECT_NE(createSql.find("CREATE VIEW"), std::string::npos);
    EXPECT_NE(createSql.find("user_order_summary"), std::string::npos);
    EXPECT_NE(createSql.find("LEFT JOIN"), std::string::npos);
    EXPECT_NE(createSql.find("GROUP BY"), std::string::npos);
    EXPECT_NE(createSql.find("HAVING"), std::string::npos);
}

/**
 * @brief Test SqlView copy and move semantics
 */
TEST_F(SqlViewTest, CopyMoveSemantics) {
    SqlView original("original_view");
    original.setQuery(createTestQuery());
    original.setSchema("test_schema");
    original.setComment("Original view comment");
    original.setUpdatable(false);
    original.addDependency("users");
    original.addDependency("orders");
    
    // Copy constructor
    SqlView copied(original);
    EXPECT_EQ(copied.name(), original.name());
    EXPECT_EQ(copied.query(), original.query());
    EXPECT_EQ(copied.schema(), original.schema());
    EXPECT_EQ(copied.comment(), original.comment());
    EXPECT_EQ(copied.isUpdatable(), original.isUpdatable());
    EXPECT_EQ(copied.dependencies().size(), original.dependencies().size());
    
    // Move constructor
    SqlView moved(std::move(original));
    EXPECT_EQ(moved.name(), "original_view");
    EXPECT_FALSE(moved.query().empty());
    EXPECT_EQ(moved.schema(), "test_schema");
    EXPECT_EQ(moved.comment(), "Original view comment");
    EXPECT_FALSE(moved.isUpdatable());
    EXPECT_EQ(moved.dependencies().size(), 2);
}

/**
 * @brief Test SqlView equality and comparison
 */
TEST_F(SqlViewTest, EqualityComparison) {
    SqlView view1("test_view");
    SqlView view2("test_view");
    SqlView view3("different_view");
    
    // Equal views
    EXPECT_TRUE(view1 == view2);
    EXPECT_FALSE(view1 != view2);
    
    // Different names
    EXPECT_FALSE(view1 == view3);
    EXPECT_TRUE(view1 != view3);
}

/**
 * @brief Test SqlView with different query types
 */
TEST_F(SqlViewTest, DifferentQueryTypes) {
    // Simple SELECT view
    SqlView simpleView("simple_view");
    simpleView.setQuery("SELECT id, name FROM users WHERE active = 1");
    EXPECT_FALSE(simpleView.query().empty());
    
    // JOIN view
    SqlView joinView("join_view");
    joinView.setQuery(R"(
        SELECT u.name, p.title 
        FROM users u 
        INNER JOIN posts p ON u.id = p.user_id
    )");
    EXPECT_FALSE(joinView.query().empty());
    
    // Aggregate view
    SqlView aggregateView("aggregate_view");
    aggregateView.setQuery(R"(
        SELECT category, COUNT(*) as count, AVG(price) as avg_price
        FROM products
        GROUP BY category
    )");
    EXPECT_FALSE(aggregateView.query().empty());
    
    // Subquery view
    SqlView subqueryView("subquery_view");
    subqueryView.setQuery(R"(
        SELECT * FROM users 
        WHERE id IN (SELECT user_id FROM orders WHERE total > 1000)
    )");
    EXPECT_FALSE(subqueryView.query().empty());
}

/**
 * @brief Test SqlView edge cases
 */
TEST_F(SqlViewTest, EdgeCases) {
    // Empty view name
    SqlView emptyView("");
    EXPECT_FALSE(emptyView.isValid());
    
    // View with special characters in name
    SqlView specialView("view_with_123");
    EXPECT_TRUE(specialView.isValid());
    EXPECT_EQ(specialView.name(), "view_with_123");
    
    // View with very long name
    std::string longName(255, 'v');
    SqlView longNameView(longName);
    EXPECT_TRUE(longNameView.isValid());
    EXPECT_EQ(longNameView.name(), longName);
    
    // View with multiline query
    SqlView multilineView("multiline_view");
    std::string multilineQuery = R"(
        SELECT 
            id,
            name,
            email
        FROM 
            users
        WHERE 
            active = 1
            AND created_at > '2023-01-01'
        ORDER BY 
            created_at DESC
    )";
    multilineView.setQuery(multilineQuery);
    EXPECT_EQ(multilineView.query(), multilineQuery);
    
    // View with empty query but valid name
    SqlView noQueryView("empty_query_view");
    EXPECT_TRUE(noQueryView.isValid());
    EXPECT_TRUE(noQueryView.query().empty());
    
    // Test duplicate dependencies
    SqlView dupDepsView("dup_deps_view");
    dupDepsView.addDependency("users");
    dupDepsView.addDependency("users"); // Duplicate
    dupDepsView.addDependency("orders");
    
    auto deps = dupDepsView.dependencies();
    // Should handle duplicates appropriately (implementation dependent)
    EXPECT_GE(deps.size(), 2); // At least users and orders
}
