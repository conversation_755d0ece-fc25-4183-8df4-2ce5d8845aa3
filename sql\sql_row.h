#ifndef DATABASE_SQL_ROW_H
#define DATABASE_SQL_ROW_H

#include <string>
#include <string_view>
#include <unordered_map>
#include <vector>
#include <chrono>
#include <optional>

#include "sql_column.h"

namespace database {

// Forward declarations
class SqlRowPrivate;

/**
 * @brief Class representing a database row
 *
 * This class encapsulates a database row with column values.
 * It provides methods for setting and getting column values.
 */
class SqlRow final : public SqlObject {
public:
    /**
     * @brief Metadata for SQL row objects (Tier 2 - Extended Metadata)
     *
     * This struct contains comprehensive descriptive information about table rows.
     */
    struct SqlRowMetadata {
        std::string tableName;                              ///< Parent table name
        std::vector<std::string> columnNames;               ///< Column names in order
        std::optional<std::chrono::system_clock::time_point> insertTime; ///< Insert timestamp
        std::optional<std::chrono::system_clock::time_point> updateTime; ///< Last update timestamp
        std::optional<std::string> version;                 ///< Row version for optimistic locking
        std::optional<size_t> rowId;                        ///< Physical row identifier
        bool isDeleted = false;                             ///< Soft delete flag
        bool isDirty = false;                               ///< Has unsaved changes
        std::unordered_set<std::string> dirtyColumns;       ///< Columns that have been modified
        std::unordered_map<std::string, std::string> properties; ///< Additional properties

        SqlRowMetadata() = default;

        template<typename TableName, typename ColumnNames>
        SqlRowMetadata(TableName&& tableName, ColumnNames&& columnNames)
            : tableName(std::forward<TableName>(tableName))
            , columnNames(std::forward<ColumnNames>(columnNames)) {}

        // Enhanced validation utilities
        [[nodiscard]] bool isValidForTable(const SqlTable& table) const;
        [[nodiscard]] std::vector<std::string> getValidationErrors(const SqlTable& table) const;

        // Change tracking
        void markDirty(std::string_view columnName) {
            isDirty = true;
            dirtyColumns.insert(std::string(columnName));
        }

        void clearDirty() noexcept {
            isDirty = false;
            dirtyColumns.clear();
        }

        [[nodiscard]] bool isColumnDirty(std::string_view columnName) const noexcept {
            return dirtyColumns.find(std::string(columnName)) != dirtyColumns.end();
        }

        [[nodiscard]] std::vector<std::string> getDirtyColumns() const {
            return std::vector<std::string>(dirtyColumns.begin(), dirtyColumns.end());
        }

        // Column management utilities
        [[nodiscard]] bool hasColumn(std::string_view columnName) const noexcept {
            return std::find(columnNames.begin(), columnNames.end(), columnName) != columnNames.end();
        }

        [[nodiscard]] std::optional<size_t> getColumnIndex(std::string_view columnName) const noexcept {
            auto it = std::find(columnNames.begin(), columnNames.end(), columnName);
            return (it != columnNames.end()) ? std::optional<size_t>(std::distance(columnNames.begin(), it)) : std::nullopt;
        }

        template<typename ColumnRange>
        void setColumns(ColumnRange&& columnRange) {
            columnNames.clear();
            if constexpr (std::is_same_v<std::decay_t<ColumnRange>, std::vector<std::string>>) {
                columnNames.reserve(columnRange.size());
            }
            for (auto&& column : columnRange) {
                columnNames.emplace_back(std::forward<decltype(column)>(column));
            }
        }
    };

    SqlRow() noexcept;
    explicit SqlRow(std::string_view tableName) noexcept;
    explicit SqlRow(std::string_view tableName, DataMap values);
    explicit SqlRow(std::string_view tableName, DataMap&& values) noexcept;
    explicit SqlRow(DataMap values);
    explicit SqlRow(DataMap&& values) noexcept;

    // Template constructor for type-safe row creation
    template<typename ValueMap, typename = std::enable_if_t<std::is_convertible_v<ValueMap, DataMap>>>
    SqlRow(std::string_view tableName, ValueMap&& values)
        : SqlRow(tableName) {
        setValues(std::forward<ValueMap>(values));
    }

    // Copy/move operations
    SqlRow(const SqlRow& other);
    SqlRow& operator=(const SqlRow& other);
    SqlRow(SqlRow&& other) noexcept;
    SqlRow& operator=(SqlRow&& other) noexcept;

    //----------------------------------------------------------------------
    // Static Methods
    //----------------------------------------------------------------------
    [[nodiscard]] static SqlRow fromDatabase(std::shared_ptr<SqlTable> table,
                                             std::string_view whereClause,
                                             bool loadMetadata = true);

    //----------------------------------------------------------------------
    // Row Metadata
    //----------------------------------------------------------------------
    [[nodiscard]] SqlRowMetadata* metadata() const noexcept;
    SqlRow& setMetadata(const SqlRowMetadata& metadata);
    [[nodiscard]] bool hasMetadata() const noexcept;
    bool loadMetadata();
    bool refreshMetadata();

    [[nodiscard]] Data value(std::string_view columnName) const;
    [[nodiscard]] Data value(size_t index) const;

    template<typename T>
    [[nodiscard]] std::optional<T> getValue(std::string_view columnName) const {
        auto it = m_values.find(std::string(columnName));
        if (it != m_values.end()) {
            try {
                return it->second.to<T>();
            } catch (...) {
                return std::nullopt;
            }
        }
        return std::nullopt;
    }

    template<typename T>
    SqlRow& setValue(std::string_view columnName, T&& value) {
        static_assert(std::is_convertible_v<T, Data>, "Value must be convertible to Data");
        m_values[std::string(columnName)] = Data(std::forward<T>(value));
        if (auto meta = metadata()) {
            meta->markDirty(columnName);
        }
        return *this;
    }

    SqlRow& setValue(std::string_view columnName, Data value);
    SqlRow& setValue(size_t index, Data value);

    [[nodiscard]] const DataMap& values() const noexcept;
    SqlRow& setValues(DataMap values);
    SqlRow& setValues(DataMap&& values) noexcept;

    [[nodiscard]] bool hasColumn(std::string_view columnName) const noexcept;
    [[nodiscard]] size_t columnCount() const noexcept;
    [[nodiscard]] std::vector<std::string> columnNames() const;
    SqlRow& removeColumn(std::string_view columnName);

    template<typename Predicate>
    [[nodiscard]] std::vector<std::string> filterColumns(Predicate&& pred) const {
        std::vector<std::string> result;
        for (const auto& [name, value] : m_values) {
            if (pred(name, value)) {
                result.push_back(name);
            }
        }
        return result;
    }

    [[nodiscard]] bool isEmpty() const noexcept;
    SqlRow& clear();

    [[nodiscard]] bool isDirty() const noexcept;
    SqlRow& setDirty(bool dirty = true) noexcept;
    SqlRow& markClean() noexcept { return setDirty(false); }

    [[nodiscard]] bool isDeleted() const noexcept;
    SqlRow& setDeleted(bool deleted = true) noexcept;
    SqlRow& markDeleted() noexcept { return setDeleted(true); }
    SqlRow& unmarkDeleted() noexcept { return setDeleted(false); }

    [[nodiscard]] std::optional<size_t> rowId() const noexcept;
    SqlRow& setRowId(std::optional<size_t> rowId) noexcept;

    [[nodiscard]] std::optional<std::string> version() const noexcept;
    SqlRow& setVersion(std::optional<std::string> version);


    [[nodiscard]] std::string_view tableName() const noexcept;
    SqlRow& setTableName(std::string tableName);

    Data& operator[](std::string_view columnName);
    const Data& operator[](std::string_view columnName) const;
    Data& operator[](size_t index);
    const Data& operator[](size_t index) const;

    //----------------------------------------------------------------------
    // Table Association
    //----------------------------------------------------------------------
    [[nodiscard]] SqlTable table() const noexcept;
    SqlRow& setTable(const SqlTable& table) noexcept;

    [[nodiscard]] std::shared_ptr<SqlDatabase> database() const override;
    void setDatabase(std::shared_ptr<SqlDatabase> db);

    bool save();
    bool insert();
    bool update(std::string_view whereClause = {});
    bool deleteFromDatabase(std::string_view whereClause = {});
    bool reload(std::string_view whereClause = {});
    [[nodiscard]] bool exists(std::string_view whereClause = {}) const;

    [[nodiscard]] bool isValid() const;
    [[nodiscard]] std::vector<std::string> validationErrors() const;
    [[nodiscard]] bool isColumnValid(std::string_view columnName) const;

    // Type-specific validation
    [[nodiscard]] bool validateAgainstTable(const SqlTable& table) const;

    //----------------------------------------------------------------------
    // Enhanced SqlObject Implementation
    //----------------------------------------------------------------------
    [[nodiscard]] std::string qualifiedName() const override;
    [[nodiscard]] std::string toSql() const override;

    [[nodiscard]] std::string toJson() const;
    [[nodiscard]] std::string toXml() const;
    [[nodiscard]] std::string toInsertStatement(std::string_view tableName = {}) const;
    [[nodiscard]] std::string toUpdateStatement(std::string_view whereClause,
                                                std::string_view tableName = {}) const;
    [[nodiscard]] std::string toDeleteStatement(std::string_view whereClause,
                                                std::string_view tableName = {}) const;

private:
    DataMap m_values;
    mutable std::shared_ptr<SqlRowMetadata> m_metadata;
    mutable std::shared_ptr<SqlRowPrivate> d_ptr;

    // Helper methods for tier management
    void ensureDatabase() const;

    // Internal helpers
    void updateColumnNames();
    Data& getValueRef(std::string_view columnName);
    const Data& getValueRef(std::string_view columnName) const;
    Data& getValueRef(size_t index);
    const Data& getValueRef(size_t index) const;
    std::string buildDefaultWhereClause() const;
};

} // namespace database

// Enhanced hash specialization for SqlRow with modern C++ features
namespace std {
template<>
struct hash<database::SqlRow> {
    [[nodiscard]] size_t operator()(const database::SqlRow& row) const noexcept {
        // Use the base SqlObject hash combined with row-specific information
        size_t h1 = row.hash();

        // Hash the table name if available
        size_t h2 = 0;
        try {
            auto tableName = row.tableName();
            if (!tableName.empty()) {
                h2 = std::hash<std::string_view>{}(tableName);
            }
        } catch (...) {
            // Ignore errors during hash calculation
        }

        // Hash the column count and dirty state for additional uniqueness
        size_t h3 = row.columnCount();
        size_t h4 = row.isDirty() ? 1 : 0;

        return h1 ^ (h2 << 1) ^ (h3 << 2) ^ (h4 << 3);
    }
};
} // namespace std

#endif // DATABASE_SQL_ROW_H
