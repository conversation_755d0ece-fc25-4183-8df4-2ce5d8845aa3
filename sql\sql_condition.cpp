#include "sql_condition.h"

#include <format>

#include "sql_column.h"

namespace database {

SqlCondition::SqlCondition() : m_paramStyle(s_defaultParamStyle) {}

SqlCondition::SqlCondition(const SqlColumn& column, SqlOperator op, const Variant& value) {
    std::string paramName = addParameter(value);
    m_condition = std::format("{} {} {}", column.qualifiedName(), sqlOperatorToString(op), paramName);
}

SqlCondition::SqlCondition(const SqlColumn& column, std::string_view opStr, const Variant& value) {
    // Convert the string operator to SqlOperator enum
    SqlOperator op = stringToSqlOperator(opStr);

    // Use the same implementation as the SqlOperator version
    std::string paramName = addParameter(value);
    m_condition = std::format("{} {} {}", column.qualifiedName(), sqlOperatorToString(op), paramName);
}

SqlCondition::SqlCondition(const SqlColumn& column, SqlOperator op) {
    m_condition = std::format("{} {}", column.qualifiedName(), sqlOperatorToString(op));
}

SqlCondition::SqlCondition(const SqlColumn& column, std::string_view opStr) {
    // Convert the string operator to SqlOperator enum
    SqlOperator op = stringToSqlOperator(opStr);

    // Use the same implementation as the SqlOperator version
    m_condition = std::format("{} {}", column.qualifiedName(), sqlOperatorToString(op));
}

SqlCondition::SqlCondition(const SqlColumn& column, SqlOperator op, const std::vector<Variant>& values) {
    if (op == SqlOperator::In || op == SqlOperator::NotIn) {
        if (values.empty()) {
            // Empty IN clause is always false
            m_condition = op == SqlOperator::In ? "0 = 1" : "1 = 1";
            return;
        }

        // Build the IN clause using std::format
        std::string inClause = std::format("{} {} (", column.qualifiedName(), sqlOperatorToString(op));

        for (size_t i = 0; i < values.size(); ++i) {
            std::string paramName = addParameter(values[i]);

            if (i > 0) {
                inClause += ", ";
            }
            inClause += paramName;
        }

        inClause += ")";
        m_condition = inClause;
    } else if (op == SqlOperator::Between && values.size() >= 2) {
        std::string minParamName = addParameter(values[0]);
        std::string maxParamName = addParameter(values[1]);

        m_condition = std::format("{} {} {} AND {}",
                                  column.qualifiedName(),
                                  sqlOperatorToString(op),
                                  minParamName,
                                  maxParamName);
    }
}

SqlCondition::SqlCondition(const SqlColumn& column, std::string_view opStr, const std::vector<Variant>& values) {
    // Convert the string operator to SqlOperator enum
    SqlOperator op = stringToSqlOperator(opStr);

    // Use the same implementation as the SqlOperator version
    if (op == SqlOperator::In || op == SqlOperator::NotIn) {
        if (values.empty()) {
            // Empty IN clause is always false
            m_condition = op == SqlOperator::In ? "0 = 1" : "1 = 1";
            return;
        }

        // Build the IN clause using std::format
        std::string inClause = std::format("{} {} (", column.qualifiedName(), sqlOperatorToString(op));

        for (size_t i = 0; i < values.size(); ++i) {
            std::string paramName = addParameter(values[i]);

            if (i > 0) {
                inClause += ", ";
            }
            inClause += paramName;
        }

        inClause += ")";
        m_condition = inClause;
    } else if (op == SqlOperator::Between && values.size() >= 2) {
        std::string minParamName = addParameter(values[0]);
        std::string maxParamName = addParameter(values[1]);

        m_condition = std::format("{} {} {} AND {}",
                                  column.qualifiedName(),
                                  sqlOperatorToString(op),
                                  minParamName,
                                  maxParamName);
    }
}

SqlCondition::SqlCondition(std::string_view condition)
    : m_condition(condition)
{
}

SqlCondition SqlCondition::operator&&(const SqlCondition& other) const {
    if (isEmpty()) {
        return other;
    }

    if (other.isEmpty()) {
        return *this;
    }

    SqlCondition result;
    result.m_condition = std::format("({}) AND ({})", m_condition, other.m_condition);
    result.m_paramStyle = m_paramStyle;

    // Merge positional parameters
    result.m_paramValues.reserve(m_paramValues.size() + other.m_paramValues.size());
    result.m_paramValues = m_paramValues;
    result.m_paramValues.insert(result.m_paramValues.end(), other.m_paramValues.begin(), other.m_paramValues.end());

    // Merge named parameters
    for (const auto& [key, value] : other.m_namedParams) {
        result.m_namedParams[key] = value;
    }

    // Copy named parameters from this condition
    for (const auto& [key, value] : m_namedParams) {
        // Only copy if not already present (from other condition)
        if (result.m_namedParams.find(key) == result.m_namedParams.end()) {
            result.m_namedParams[key] = value;
        }
    }

    return result;
}

SqlCondition SqlCondition::operator||(const SqlCondition& other) const {
    if (isEmpty()) {
        return other;
    }

    if (other.isEmpty()) {
        return *this;
    }

    SqlCondition result;
    result.m_condition = std::format("({}) OR ({})", m_condition, other.m_condition);
    result.m_paramStyle = m_paramStyle;

    // Merge positional parameters
    result.m_paramValues.reserve(m_paramValues.size() + other.m_paramValues.size());
    result.m_paramValues = m_paramValues;
    result.m_paramValues.insert(result.m_paramValues.end(), other.m_paramValues.begin(), other.m_paramValues.end());

    // Merge named parameters
    for (const auto& [key, value] : other.m_namedParams) {
        result.m_namedParams[key] = value;
    }

    // Copy named parameters from this condition
    for (const auto& [key, value] : m_namedParams) {
        // Only copy if not already present (from other condition)
        if (result.m_namedParams.find(key) == result.m_namedParams.end()) {
            result.m_namedParams[key] = value;
        }
    }

    return result;
}

SqlCondition SqlCondition::operator!() const {
    if (isEmpty()) {
        return *this;
    }

    SqlCondition result;
    result.m_condition = std::format("NOT ({})", m_condition);
    result.m_paramStyle = m_paramStyle;

    // Copy positional parameters
    result.m_paramValues = m_paramValues;

    // Copy named parameters
    result.m_namedParams = m_namedParams;

    return result;
}

std::string SqlCondition::toSql() const {
    return m_condition;
}

void SqlCondition::setDefaultParameterStyle(ParameterStyle style) {
    s_defaultParamStyle = style;
}

ParameterStyle SqlCondition::defaultParameterStyle() {
    return s_defaultParamStyle;
}

SqlCondition& SqlCondition::setParameterStyle(ParameterStyle style) {
    m_paramStyle = style;
    return *this;
}

ParameterStyle SqlCondition::parameterStyle() const noexcept {
    return m_paramStyle;
}

std::string SqlCondition::generateParamName() const {
    return std::format("param_{}", ++m_paramCounter);
}

std::string SqlCondition::formatPlaceholder(std::string_view name) const {
    switch (m_paramStyle) {
    case ParameterStyle::QuestionMark:
        return "?";
    case ParameterStyle::NamedColon:
        return std::format(":{}", name);
    case ParameterStyle::NamedAt:
        return std::format("@{}", name);
    case ParameterStyle::IndexedDollar:
        return std::format("${}", m_paramValues.size());
    default:
        return "?";
    }
}

std::string SqlCondition::addParameter(const Variant& value) {
    std::string name = generateParamName();
    return addNamedParameter(name, value);
}

std::string SqlCondition::addNamedParameter(std::string_view name, const Variant& value) {
    // Store the value in both storage mechanisms for compatibility
    m_paramValues.push_back(value);

    // Only store in named map if using a named parameter style
    if (m_paramStyle != ParameterStyle::QuestionMark) {
        m_namedParams[std::string(name)] = value;
    }

    return formatPlaceholder(name);
}

} // namespace database
