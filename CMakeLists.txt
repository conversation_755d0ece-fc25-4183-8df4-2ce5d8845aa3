cmake_minimum_required(VERSION 3.14)
project(database_test VERSION 0.1.0 LANGUAGES CXX)

# Set C++ standard
set(CMAKE_CXX_STANDARD 20)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)

# Set build type if not specified
if(NOT CMAKE_BUILD_TYPE)
    set(CMAKE_BUILD_TYPE Debug)
endif()

# Output directories
set(CMAKE_ARCHIVE_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)
set(CMAKE_LIBRARY_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin)

# Compiler flags
if(MSVC)
    add_compile_options(/W4 /permissive- /Zc:__cplusplus /EHsc)
    # Enable C++20 features
    add_compile_options(/std:c++20)
else()
    add_compile_options(-Wall -Wextra -Wpedantic -Werror=return-type)
    # Enable thread safety analysis if using Clang
    if(CMAKE_CXX_COMPILER_ID MATCHES "Clang")
        add_compile_options(-Wthread-safety)
    endif()
endif()

# Use local GoogleTest from third_party directory
set(GTEST_ROOT ${CMAKE_CURRENT_SOURCE_DIR}/third_party/googletest-distribution)
set(GTEST_INCLUDE_DIRS ${GTEST_ROOT}/include)
set(GTEST_LIBRARIES ${GTEST_ROOT}/lib/gtest.lib)
set(GTEST_MAIN_LIBRARIES ${GTEST_ROOT}/lib/gtest_main.lib)
set(GMOCK_LIBRARIES ${GTEST_ROOT}/lib/gmock.lib)
set(GMOCK_MAIN_LIBRARIES ${GTEST_ROOT}/lib/gmock_main.lib)

# Create imported targets for GoogleTest
add_library(GTest::gtest STATIC IMPORTED)
set_target_properties(GTest::gtest PROPERTIES
    IMPORTED_LOCATION ${GTEST_LIBRARIES}
    INTERFACE_INCLUDE_DIRECTORIES ${GTEST_INCLUDE_DIRS}
)

add_library(GTest::gtest_main STATIC IMPORTED)
set_target_properties(GTest::gtest_main PROPERTIES
    IMPORTED_LOCATION ${GTEST_MAIN_LIBRARIES}
    INTERFACE_INCLUDE_DIRECTORIES ${GTEST_INCLUDE_DIRS}
)

add_library(GTest::gmock STATIC IMPORTED)
set_target_properties(GTest::gmock PROPERTIES
    IMPORTED_LOCATION ${GMOCK_LIBRARIES}
    INTERFACE_INCLUDE_DIRECTORIES ${GTEST_INCLUDE_DIRS}
)

add_library(GTest::gmock_main STATIC IMPORTED)
set_target_properties(GTest::gmock_main PROPERTIES
    IMPORTED_LOCATION ${GMOCK_MAIN_LIBRARIES}
    INTERFACE_INCLUDE_DIRECTORIES ${GTEST_INCLUDE_DIRS}
)

# Include directories
include_directories(
    ${CMAKE_CURRENT_SOURCE_DIR}
    ${CMAKE_CURRENT_SOURCE_DIR}/types
    ${CMAKE_CURRENT_SOURCE_DIR}/utils
    ${GTEST_INCLUDE_DIRS}
)

# Source files - only include SQL objects for testing
set(SOURCES
    sql/sql_object.cpp
    sql/sql_column.cpp
    sql/sql_index.cpp
    sql/sql_row.cpp
    sql/sql_table.cpp
    sql/sql_view.cpp
)

# Create the main library
add_library(database_lib STATIC ${SOURCES})

target_include_directories(database_lib PRIVATE
    third_party/sqlite
)
target_link_directories(database_lib PRIVATE
    third_party/sqlite
)
target_link_libraries(database_lib PRIVATE ${CMAKE_CURRENT_SOURCE_DIR}/third_party/sqlite/sqlite3.lib)

# Enable testing
enable_testing()

# Add tests directory
add_subdirectory(tests)

# Add example executable
add_executable(sql_objects_example examples/sql_objects_example.cpp)
target_link_libraries(sql_objects_example PRIVATE database_lib)
target_compile_features(sql_objects_example PRIVATE cxx_std_20)

# Copy SQLite DLL to example output directory on Windows
if(WIN32)
    add_custom_command(TARGET sql_objects_example POST_BUILD
        COMMAND ${CMAKE_COMMAND} -E copy_if_different
        "${CMAKE_CURRENT_SOURCE_DIR}/third_party/sqlite/sqlite3.dll"
        $<TARGET_FILE_DIR:sql_objects_example>
    )
endif()

# Add benchmarks directory
# add_subdirectory(benchmarks)

# SQLite DLL will be copied in the tests/CMakeLists.txt file
