#ifndef DATABASE_SQL_ENUMS_H
#define DATABASE_SQL_ENUMS_H

#include <string_view>

namespace database {

// Forward declaration for FieldType
enum class FieldType;

/**
 * @brief Error codes for database operations
 *
 * Error codes are organized into categories by ranges:
 * - General errors:     0-99
 * - Connection errors:  100-199
 * - Execution errors:   200-299
 * - Transaction errors: 300-399
 * - Constraint errors:  400-499
 * - Resource errors:    500-599
 */
enum class ErrorCode : int16_t {
    // General errors (0-99)
    Unknown = 0,                  ///< Unknown or unspecified error
    InvalidArgument = 1,          ///< Invalid argument or parameter
    NotImplemented = 2,           ///< Feature or operation not implemented
    OperationCancelled = 3,       ///< Operation was cancelled
    InternalError = 4,            ///< Internal error in the database system

    // Connection errors (100-199)
    ConnectionFailed = 100,       ///< Failed to establish connection
    ConnectionTimeout = 101,      ///< Connection timed out
    ConnectionClosed = 102,       ///< Connection is closed
    AuthenticationFailed = 103,   ///< Authentication failed
    NetworkError = 104,           ///< Network-related error

    // Execution errors (200-299)
    ExecutionFailed = 200,        ///< General execution failure
    StatementInvalid = 201,       ///< Statement is invalid
    ParameterBindingFailed = 202, ///< Failed to bind parameters
    ResultSetClosed = 203,        ///< Result set is closed
    QueryTimeout = 204,           ///< Query execution timed out
    SyntaxError = 205,            ///< SQL syntax error

    // Transaction errors (300-399)
    TransactionFailed = 300,      ///< General transaction failure
    TransactionAlreadyActive = 301, ///< Transaction is already active
    TransactionNotActive = 302,   ///< No active transaction
    TransactionRollbackOnly = 303, ///< Transaction is marked for rollback only
    DeadlockDetected = 304,       ///< Deadlock was detected

    // Constraint errors (400-499)
    ConstraintViolation = 400,    ///< General constraint violation
    UniqueConstraintViolation = 401, ///< Unique constraint violation
    ForeignKeyConstraintViolation = 402, ///< Foreign key constraint violation
    CheckConstraintViolation = 403, ///< Check constraint violation
    NotNullConstraintViolation = 404, ///< Not null constraint violation

    // Resource errors (500-599)
    ResourceError = 500,          ///< General resource error
    DatabaseLocked = 501,         ///< Database is locked
    ResultInvalid = 502,          ///< Result is invalid
    OutOfMemory = 503,            ///< Out of memory
    DiskFull = 504,               ///< Disk is full
    TooManyConnections = 505      ///< Too many connections
};

/**
 * @brief Enumeration for synchronization states between local object definition and database reality
 */
enum class SqlSyncState {
    Unsynced,           ///< Local definition exists, not yet synchronized with database
    Synced,             ///< Local definition matches database state
    Conflicted,         ///< Local definition conflicts with database state
    DatabaseOnly,       ///< Object exists only in database (accessor mode)
    LocalOnly           ///< Object exists only locally (definer mode)
};

/**
 * @brief Enumeration for conflict resolution strategies
 */
enum class SqlConflictResolution {
    PreferLocal,        ///< Use local definition, overwrite database
    PreferDatabase,     ///< Use database state, overwrite local
    Manual,             ///< Require manual resolution
    Merge               ///< Attempt to merge compatible changes
};

/**
 * @brief Enumeration of SQL database object types
 */
enum class SqlObjectType : uint8_t {
    Unknown,        ///< Unknown object type
    Table,          ///< Database table
    Column,         ///< Table column
    Index,          ///< Database index
    View,           ///< Database view
    Row,            ///< Table row
    Schema,         ///< Database schema
    Trigger,        ///< Database trigger
    Procedure,      ///< Stored procedure
    Function        ///< Database function
};

/**
 * @brief SQL data types for columns
 */
enum class SqlDataType : uint8_t {
    Unknown,        ///< Unknown data type
    Integer,        ///< Integer type
    BigInt,         ///< Big integer type
    SmallInt,       ///< Small integer type
    TinyInt,        ///< Tiny integer type
    Real,           ///< Real number type
    Float,          ///< Float type
    Double,         ///< Double precision type
    Decimal,        ///< Decimal type
    Numeric,        ///< Numeric type
    Varchar,        ///< Variable character type
    Char,           ///< Fixed character type
    Text,           ///< Text type
    Blob,           ///< Binary large object
    Binary,         ///< Binary data
    Date,           ///< Date type
    Time,           ///< Time type
    DateTime,       ///< Date and time type
    Timestamp,      ///< Timestamp type
    Boolean         ///< Boolean type
};

/**
 * @brief Index types
 */
enum class SqlIndexType : uint8_t {
    Unknown,        ///< Unknown index type
    Primary,        ///< Primary key index
    Unique,         ///< Unique index
    Normal,         ///< Normal index
    Clustered,      ///< Clustered index
    NonClustered,   ///< Non-clustered index
    Bitmap,         ///< Bitmap index
    Hash,           ///< Hash index
    FullText        ///< Full-text index
};

/**
 * @brief Column constraints
 */
enum class SqlColumnConstraint : uint8_t {
    None = 0,           ///< No constraints
    NotNull = 1 << 0,   ///< NOT NULL constraint
    Unique = 1 << 1,    ///< UNIQUE constraint
    PrimaryKey = 1 << 2, ///< PRIMARY KEY constraint
    ForeignKey = 1 << 3, ///< FOREIGN KEY constraint
    Check = 1 << 4,     ///< CHECK constraint
    Default = 1 << 5,   ///< DEFAULT constraint
    AutoIncrement = 1 << 6 ///< AUTO_INCREMENT constraint
};

// Enable bitwise operations for SqlColumnConstraint
constexpr SqlColumnConstraint operator|(SqlColumnConstraint lhs, SqlColumnConstraint rhs) noexcept {
    using UT = std::underlying_type_t<SqlColumnConstraint>;
    return static_cast<SqlColumnConstraint>(static_cast<UT>(lhs) | static_cast<UT>(rhs));
}

constexpr SqlColumnConstraint operator&(SqlColumnConstraint lhs, SqlColumnConstraint rhs) noexcept {
    using UT = std::underlying_type_t<SqlColumnConstraint>;
    return static_cast<SqlColumnConstraint>(static_cast<UT>(lhs) & static_cast<UT>(rhs));
}

constexpr SqlColumnConstraint operator~(SqlColumnConstraint flag) {
    using UT = std::underlying_type_t<SqlColumnConstraint>;
    return static_cast<SqlColumnConstraint>(~static_cast<UT>(flag));
}

constexpr SqlColumnConstraint& operator|=(SqlColumnConstraint& lhs, SqlColumnConstraint rhs) noexcept {
    lhs = lhs | rhs;
    return lhs;
}

constexpr SqlColumnConstraint& operator&=(SqlColumnConstraint& lhs, SqlColumnConstraint rhs) noexcept {
    lhs = lhs & rhs;
    return lhs;
}

constexpr bool hasConstraint(SqlColumnConstraint constraints, SqlColumnConstraint constraint) noexcept {
    return (constraints & constraint) != SqlColumnConstraint::None;
}

/**
 * @brief Transaction operations
 */
enum class TransactionOperation : uint8_t {
    Unknown,
    Begin,
    Commit,
    Rollback,
    SavePoint,
    RollbackToSavePoint,
    ReleaseSavePoint
};

/**
 * @brief Enumeration of SQL comparison operators
 */
enum class SqlConditionOperator : uint8_t {
    Equal,              // =
    NotEqual,           // <>
    LessThan,           // <
    LessEqual,          // <=
    GreaterThan,        // >
    GreaterEqual,       // >=
    Like,               // LIKE
    NotLike,            // NOT LIKE
    In,                 // IN
    NotIn,              // NOT IN
    Between,            // BETWEEN
    IsNull,             // IS NULL
    IsNotNull           // IS NOT NULL
};

/**
 * @brief Enumeration of SQL logical operators
 */
enum class SqlLogicalOperator : uint8_t {
    And,                // AND
    Or,                 // OR
    Not                 // NOT
};

/**
 * @brief Enumeration of SQL sort orders
 */
enum class SqlSortOrder : uint8_t {
    Ascending,          // ASC
    Descending          // DESC
};

/**
 * @brief Enumeration of SQL join types
 */
enum class SqlJoinType : uint8_t {
    Inner,              // INNER JOIN
    Left,               // LEFT JOIN
    Right,              // RIGHT JOIN
    Full,               // FULL JOIN
    Cross               // CROSS JOIN
};

/**
 * @brief Convert a SqlConditionOperator to its SQL string representation
 * @param op The operator to convert
 * @return The SQL string representation
 */
[[nodiscard]] constexpr std::string_view sqlOperatorToString(SqlConditionOperator op) noexcept {
    switch (op) {
        case SqlConditionOperator::Equal:        return "=";
        case SqlConditionOperator::NotEqual:     return "<>";
        case SqlConditionOperator::LessThan:     return "<";
        case SqlConditionOperator::LessEqual:    return "<=";
        case SqlConditionOperator::GreaterThan:  return ">";
        case SqlConditionOperator::GreaterEqual: return ">=";
        case SqlConditionOperator::Like:         return "LIKE";
        case SqlConditionOperator::NotLike:      return "NOT LIKE";
        case SqlConditionOperator::In:           return "IN";
        case SqlConditionOperator::NotIn:        return "NOT IN";
        case SqlConditionOperator::Between:      return "BETWEEN";
        case SqlConditionOperator::IsNull:       return "IS NULL";
        case SqlConditionOperator::IsNotNull:    return "IS NOT NULL";
        default:                                 return "=";
    }
}

/**
 * @brief Convert a string to SqlDataType enumeration
 * @param typeStr The string representation of the data type
 * @return The corresponding SqlDataType enumeration value
 */
[[nodiscard]] constexpr SqlDataType stringToSqlDataType(std::string_view typeStr) noexcept {
    if (typeStr == "INTEGER" || typeStr == "INT") return SqlDataType::Integer;
    if (typeStr == "REAL" || typeStr == "FLOAT" || typeStr == "DOUBLE") return SqlDataType::Real;
    if (typeStr == "TEXT" || typeStr == "VARCHAR" || typeStr == "CHAR") return SqlDataType::Text;
    if (typeStr == "BLOB") return SqlDataType::Blob;
    if (typeStr == "NULL") return SqlDataType::Unknown;
    if (typeStr == "NUMERIC" || typeStr == "DECIMAL") return SqlDataType::Numeric;
    if (typeStr == "BOOLEAN" || typeStr == "BOOL") return SqlDataType::Boolean;
    if (typeStr == "DATE") return SqlDataType::Date;
    if (typeStr == "TIME") return SqlDataType::Time;
    if (typeStr == "DATETIME" || typeStr == "TIMESTAMP") return SqlDataType::DateTime;
    return SqlDataType::Text; // Default fallback
}

/**
 * @brief Convert a SqlSortOrder to its SQL string representation
 * @param order The sort order to convert
 * @return The SQL string representation
 */
[[nodiscard]] constexpr std::string_view sqlSortOrderToString(SqlSortOrder order) noexcept {
    switch (order) {
        case SqlSortOrder::Ascending:  return "ASC";
        case SqlSortOrder::Descending: return "DESC";
        default:                       return "ASC";
    }
}

/**
 * @brief Convert a SqlJoinType to its SQL string representation
 * @param joinType The join type to convert
 * @return The SQL string representation
 */
[[nodiscard]] constexpr std::string_view sqlJoinTypeToString(SqlJoinType joinType) noexcept {
    switch (joinType) {
        case SqlJoinType::Inner: return "INNER JOIN";
        case SqlJoinType::Left:  return "LEFT JOIN";
        case SqlJoinType::Right: return "RIGHT JOIN";
        case SqlJoinType::Full:  return "FULL JOIN";
        case SqlJoinType::Cross: return "CROSS JOIN";
        default:                 return "INNER JOIN";
    }
}

/**
 * @brief Convert a SqlLogicalOperator to its SQL string representation
 * @param op The logical operator to convert
 * @return The SQL string representation
 */
[[nodiscard]] constexpr std::string_view sqlLogicalOperatorToString(SqlLogicalOperator op) noexcept {
    switch (op) {
        case SqlLogicalOperator::And: return "AND";
        case SqlLogicalOperator::Or:  return "OR";
        case SqlLogicalOperator::Not: return "NOT";
        default:                      return "AND";
    }
}

/**
 * @brief Convert a FieldType to SqlDataType
 * @param fieldType The field type to convert
 * @return The corresponding SqlDataType enumeration value
 */
[[nodiscard]] constexpr SqlDataType fieldTypeToSqlDataType(FieldType fieldType) noexcept {
    switch (fieldType) {
        case FieldType::Unknown:    return SqlDataType::Unknown;
        case FieldType::Integer:    return SqlDataType::Integer;
        case FieldType::BigInt:     return SqlDataType::BigInt;
        case FieldType::Float:      return SqlDataType::Float;
        case FieldType::Double:     return SqlDataType::Double;
        case FieldType::Decimal:    return SqlDataType::Decimal;
        case FieldType::Char:       return SqlDataType::Char;
        case FieldType::VarChar:    return SqlDataType::Varchar;
        case FieldType::Text:       return SqlDataType::Text;
        case FieldType::Date:       return SqlDataType::Date;
        case FieldType::Time:       return SqlDataType::Time;
        case FieldType::DateTime:   return SqlDataType::DateTime;
        case FieldType::Timestamp:  return SqlDataType::Timestamp;
        case FieldType::Boolean:    return SqlDataType::Boolean;
        case FieldType::Blob:       return SqlDataType::Blob;
        case FieldType::Binary:     return SqlDataType::Binary;
        default:                    return SqlDataType::Unknown;
    }
}

/**
 * @brief Convert a SqlDataType to its SQL string representation
 * @param dataType The data type to convert
 * @return The SQL string representation
 */
[[nodiscard]] constexpr std::string_view sqlDataTypeToString(SqlDataType dataType) noexcept {
    switch (dataType) {
    case SqlDataType::Unknown:    return "UNKNOWN";
    case SqlDataType::Integer:    return "INTEGER";
    case SqlDataType::BigInt:     return "BIGINT";
    case SqlDataType::SmallInt:   return "SMALLINT";
    case SqlDataType::TinyInt:    return "TINYINT";
    case SqlDataType::Real:       return "REAL";
    case SqlDataType::Float:      return "FLOAT";
    case SqlDataType::Double:     return "DOUBLE";
    case SqlDataType::Decimal:    return "DECIMAL";
    case SqlDataType::Numeric:    return "NUMERIC";
    case SqlDataType::Varchar:    return "VARCHAR";
    case SqlDataType::Char:       return "CHAR";
    case SqlDataType::Text:       return "TEXT";
    case SqlDataType::Blob:       return "BLOB";
    case SqlDataType::Binary:     return "BINARY";
    case SqlDataType::Date:       return "DATE";
    case SqlDataType::Time:       return "TIME";
    case SqlDataType::DateTime:   return "DATETIME";
    case SqlDataType::Timestamp:  return "TIMESTAMP";
    case SqlDataType::Boolean:    return "BOOLEAN";
    default:                      return "UNKNOWN";
    }
}

/**
 * @brief Convert a SqlIndexType to its SQL string representation
 * @param indexType The index type to convert
 * @return The SQL string representation
 */
[[nodiscard]] constexpr std::string_view sqlIndexTypeToString(SqlIndexType indexType) noexcept {
    switch (indexType) {
    case SqlIndexType::Unknown:      return "UNKNOWN";
    case SqlIndexType::Primary:      return "PRIMARY";
    case SqlIndexType::Unique:       return "UNIQUE";
    case SqlIndexType::Normal:       return "INDEX";
    case SqlIndexType::Clustered:    return "CLUSTERED";
    case SqlIndexType::NonClustered: return "NONCLUSTERED";
    case SqlIndexType::Bitmap:       return "BITMAP";
    case SqlIndexType::Hash:         return "HASH";
    case SqlIndexType::FullText:     return "FULLTEXT";
    default:                         return "INDEX";
    }
}

/**
 * @brief Convert a string to its SqlConditionOperator enum value
 * @param opStr The string representation of the operator
 * @return The SqlConditionOperator enum value, or SqlConditionOperator::Equal if not recognized
 */
[[nodiscard]] inline SqlConditionOperator stringToSqlOperator(std::string_view opStr) noexcept {
    if (opStr == "=" || opStr == "==") return SqlConditionOperator::Equal;
    if (opStr == "<>" || opStr == "!=") return SqlConditionOperator::NotEqual;
    if (opStr == "<") return SqlConditionOperator::LessThan;
    if (opStr == "<=") return SqlConditionOperator::LessEqual;
    if (opStr == ">") return SqlConditionOperator::GreaterThan;
    if (opStr == ">=") return SqlConditionOperator::GreaterEqual;
    if (opStr == "LIKE" || opStr == "like") return SqlConditionOperator::Like;
    if (opStr == "NOT LIKE" || opStr == "not like") return SqlConditionOperator::NotLike;
    if (opStr == "IN" || opStr == "in") return SqlConditionOperator::In;
    if (opStr == "NOT IN" || opStr == "not in") return SqlConditionOperator::NotIn;
    if (opStr == "BETWEEN" || opStr == "between") return SqlConditionOperator::Between;
    if (opStr == "IS NULL" || opStr == "is null") return SqlConditionOperator::IsNull;
    if (opStr == "IS NOT NULL" || opStr == "is not null") return SqlConditionOperator::IsNotNull;

    // Default to Equal if not recognized
    return SqlConditionOperator::Equal;
}

} // namespace database

#endif // DATABASE_SQL_ENUMS_H
