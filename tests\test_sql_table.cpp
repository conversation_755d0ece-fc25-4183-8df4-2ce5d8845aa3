/**
 * @file test_sql_table.cpp
 * @brief Unit tests for SqlTable class
 */

#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include "test_utils.h"
#include "sql/sql_table.h"
#include "sql/sql_column.h"
#include "sql/sql_index.h"

using namespace database;
using namespace database::test;
using ::testing::_;
using ::testing::Return;

/**
 * @brief Test fixture for SqlTable tests
 */
class SqlTableTest : public SqlObjectTestBase {
protected:
    void SetUp() override {
        SqlObjectTestBase::SetUp();
    }
    
    std::vector<SqlColumn> createTestColumns() {
        std::vector<SqlColumn> columns;
        
        SqlColumn idCol("id", SqlDataType::Integer);
        idCol.setConstraints(SqlColumnConstraint::PrimaryKey | SqlColumnConstraint::AutoIncrement);
        columns.push_back(idCol);
        
        SqlColumn nameCol("name", SqlDataType::Varchar);
        nameCol.setLength(100);
        nameCol.setConstraints(SqlColumnConstraint::NotNull);
        columns.push_back(nameCol);
        
        SqlColumn emailCol("email", SqlDataType::Varchar);
        emailCol.setLength(255);
        emailCol.setConstraints(SqlColumnConstraint::Unique);
        columns.push_back(emailCol);
        
        return columns;
    }
};

/**
 * @brief Test SqlTable default constructor
 */
TEST_F(SqlTableTest, DefaultConstructor) {
    SqlTable table;
    
    EXPECT_TRUE(table.name().empty());
    EXPECT_EQ(table.objectType(), SqlObjectType::Table);
    EXPECT_FALSE(table.isValid());
    EXPECT_EQ(table.columnCount(), 0);
}

/**
 * @brief Test SqlTable constructor with name
 */
TEST_F(SqlTableTest, ConstructorWithName) {
    const std::string tableName = "users";
    SqlTable table(tableName);
    
    EXPECT_EQ(table.name(), tableName);
    EXPECT_EQ(table.objectType(), SqlObjectType::Table);
    EXPECT_TRUE(table.isValid());
    EXPECT_EQ(table.columnCount(), 0);
}

/**
 * @brief Test SqlTable constructor with name and schema
 */
TEST_F(SqlTableTest, ConstructorWithNameAndSchema) {
    const std::string tableName = "users";
    const std::string schemaName = "public";
    SqlTable table(tableName, schemaName);
    
    EXPECT_EQ(table.name(), tableName);
    EXPECT_EQ(table.objectType(), SqlObjectType::Table);
    EXPECT_TRUE(table.isValid());
    
    if (auto metadata = table.metadata()) {
        EXPECT_EQ(metadata->schema, schemaName);
    }
}

/**
 * @brief Test SqlTable constructor with columns
 */
TEST_F(SqlTableTest, ConstructorWithColumns) {
    const std::string tableName = "users";
    auto columns = createTestColumns();
    
    SqlTable table(tableName, columns);
    
    EXPECT_EQ(table.name(), tableName);
    EXPECT_TRUE(table.isValid());
    EXPECT_EQ(table.columnCount(), columns.size());
}

/**
 * @brief Test SqlTable metadata management
 */
TEST_F(SqlTableTest, MetadataManagement) {
    SqlTable table("test_table");
    
    // Get metadata (should be created automatically)
    auto metadata = table.metadata();
    EXPECT_TRUE(metadata != nullptr);
    
    // Set metadata properties
    metadata->schema = "test_schema";
    metadata->engine = "InnoDB";
    metadata->comment = "Test table for unit tests";
    metadata->charset = "utf8mb4";
    metadata->collation = "utf8mb4_unicode_ci";
    
    // Verify metadata
    EXPECT_EQ(metadata->schema, "test_schema");
    EXPECT_EQ(metadata->engine, "InnoDB");
    EXPECT_EQ(metadata->comment, "Test table for unit tests");
    EXPECT_EQ(metadata->charset, "utf8mb4");
    EXPECT_EQ(metadata->collation, "utf8mb4_unicode_ci");
}

/**
 * @brief Test SqlTable column management
 */
TEST_F(SqlTableTest, ColumnManagement) {
    SqlTable table("users");
    auto columns = createTestColumns();
    
    // Initially no columns
    EXPECT_EQ(table.columnCount(), 0);
    EXPECT_FALSE(table.hasColumn("id"));
    
    // Add columns via metadata
    if (auto metadata = table.metadata()) {
        metadata->setColumns(columns);
    }
    
    EXPECT_EQ(table.columnCount(), 3);
    EXPECT_TRUE(table.hasColumn("id"));
    EXPECT_TRUE(table.hasColumn("name"));
    EXPECT_TRUE(table.hasColumn("email"));
    
    // Get column by name
    auto idColumn = table.column("id");
    EXPECT_TRUE(idColumn.has_value());
    EXPECT_EQ(idColumn->name(), "id");
    EXPECT_EQ(idColumn->dataType(), SqlDataType::Integer);
    EXPECT_TRUE(idColumn->isPrimaryKey());
    
    // Get column by index
    auto nameColumn = table.columnAt(1);
    EXPECT_TRUE(nameColumn.has_value());
    EXPECT_EQ(nameColumn->name(), "name");
    EXPECT_EQ(nameColumn->dataType(), SqlDataType::Varchar);
    
    // Test non-existent column
    auto nonExistent = table.column("non_existent");
    EXPECT_FALSE(nonExistent.has_value());
    
    // Test out-of-bounds index
    auto outOfBounds = table.columnAt(10);
    EXPECT_FALSE(outOfBounds.has_value());
}

/**
 * @brief Test SqlTable index management
 */
TEST_F(SqlTableTest, IndexManagement) {
    SqlTable table("users");
    auto columns = createTestColumns();
    
    if (auto metadata = table.metadata()) {
        metadata->setColumns(columns);
    }
    
    // Initially no indexes
    EXPECT_EQ(table.indexCount(), 0);
    EXPECT_FALSE(table.hasIndex("idx_name"));
    
    // Add indexes
    SqlIndex nameIndex("idx_name", SqlIndexType::Normal);
    nameIndex.addColumn("name");
    
    SqlIndex emailIndex("idx_unique_email", SqlIndexType::Unique);
    emailIndex.addColumn("email");
    
    if (auto metadata = table.metadata()) {
        metadata->indexes.push_back(nameIndex);
        metadata->indexes.push_back(emailIndex);
    }
    
    EXPECT_EQ(table.indexCount(), 2);
    EXPECT_TRUE(table.hasIndex("idx_name"));
    EXPECT_TRUE(table.hasIndex("idx_unique_email"));
    
    // Get index by name
    auto retrievedIndex = table.index("idx_name");
    EXPECT_TRUE(retrievedIndex.has_value());
    EXPECT_EQ(retrievedIndex->name(), "idx_name");
    EXPECT_EQ(retrievedIndex->indexType(), SqlIndexType::Normal);
    
    // Test non-existent index
    auto nonExistent = table.index("non_existent");
    EXPECT_FALSE(nonExistent.has_value());
}

/**
 * @brief Test SqlTable schema management
 */
TEST_F(SqlTableTest, SchemaManagement) {
    SqlTable table("test_table");
    
    // Default schema should be empty
    EXPECT_TRUE(table.schema().empty());
    
    // Set schema
    table.setSchema("public");
    EXPECT_EQ(table.schema(), "public");
    
    // Change schema
    table.setSchema("test_schema");
    EXPECT_EQ(table.schema(), "test_schema");
}

/**
 * @brief Test SqlTable alias management
 */
TEST_F(SqlTableTest, AliasManagement) {
    SqlTable table("users");
    
    // Default alias should be empty
    EXPECT_TRUE(table.alias().empty());
    
    // Set alias
    table.setAlias("u");
    EXPECT_EQ(table.alias(), "u");
    
    // Change alias
    table.setAlias("user_table");
    EXPECT_EQ(table.alias(), "user_table");
    
    // Clear alias
    table.setAlias("");
    EXPECT_TRUE(table.alias().empty());
}

/**
 * @brief Test SqlTable SQL generation
 */
TEST_F(SqlTableTest, SqlGeneration) {
    SqlTable table("users");
    auto columns = createTestColumns();
    
    if (auto metadata = table.metadata()) {
        metadata->setColumns(columns);
        metadata->engine = "InnoDB";
        metadata->comment = "User information table";
    }
    
    // Test basic SQL generation
    std::string sql = table.toSql();
    EXPECT_FALSE(sql.empty());
    EXPECT_NE(sql.find("users"), std::string::npos);
    
    // Test CREATE SQL generation
    std::string createSql = table.createSql();
    EXPECT_FALSE(createSql.empty());
    EXPECT_NE(createSql.find("CREATE TABLE"), std::string::npos);
    EXPECT_NE(createSql.find("users"), std::string::npos);
    EXPECT_NE(createSql.find("id"), std::string::npos);
    EXPECT_NE(createSql.find("name"), std::string::npos);
    EXPECT_NE(createSql.find("email"), std::string::npos);
    
    // Test DROP SQL generation
    std::string dropSql = table.dropSql();
    EXPECT_FALSE(dropSql.empty());
    EXPECT_NE(dropSql.find("DROP TABLE"), std::string::npos);
    EXPECT_NE(dropSql.find("users"), std::string::npos);
    
    // Test TRUNCATE SQL generation
    std::string truncateSql = table.truncateSql();
    EXPECT_FALSE(truncateSql.empty());
    EXPECT_NE(truncateSql.find("TRUNCATE"), std::string::npos);
    EXPECT_NE(truncateSql.find("users"), std::string::npos);
}

/**
 * @brief Test SqlTable qualified name
 */
TEST_F(SqlTableTest, QualifiedName) {
    SqlTable table("users");
    
    // Without schema
    std::string qualifiedName = table.qualifiedName();
    EXPECT_EQ(qualifiedName, "users");
    
    // With schema
    table.setSchema("public");
    qualifiedName = table.qualifiedName();
    EXPECT_EQ(qualifiedName, "public.users");
    
    // With alias
    table.setAlias("u");
    qualifiedName = table.qualifiedName();
    EXPECT_EQ(qualifiedName, "public.users AS u");
}

/**
 * @brief Test SqlTable primary key management
 */
TEST_F(SqlTableTest, PrimaryKeyManagement) {
    SqlTable table("users");
    auto columns = createTestColumns();
    
    if (auto metadata = table.metadata()) {
        metadata->setColumns(columns);
    }
    
    // Test primary key detection
    auto primaryKey = table.primaryKey();
    EXPECT_TRUE(primaryKey.has_value());
    EXPECT_EQ(primaryKey->name(), "id");
    EXPECT_TRUE(primaryKey->isPrimaryKey());
    
    // Test primary key columns
    auto pkColumns = table.primaryKeyColumns();
    EXPECT_EQ(pkColumns.size(), 1);
    EXPECT_EQ(pkColumns[0].name(), "id");
}

/**
 * @brief Test SqlTable foreign key management
 */
TEST_F(SqlTableTest, ForeignKeyManagement) {
    SqlTable table("orders");
    
    // Add columns including foreign key
    std::vector<SqlColumn> columns;
    
    SqlColumn idCol("id", SqlDataType::Integer);
    idCol.setConstraints(SqlColumnConstraint::PrimaryKey | SqlColumnConstraint::AutoIncrement);
    columns.push_back(idCol);
    
    SqlColumn userIdCol("user_id", SqlDataType::Integer);
    userIdCol.setConstraints(SqlColumnConstraint::ForeignKey | SqlColumnConstraint::NotNull);
    columns.push_back(userIdCol);
    
    if (auto metadata = table.metadata()) {
        metadata->setColumns(columns);
    }
    
    // Test foreign key detection
    auto foreignKeys = table.foreignKeyColumns();
    EXPECT_EQ(foreignKeys.size(), 1);
    EXPECT_EQ(foreignKeys[0].name(), "user_id");
    EXPECT_TRUE(foreignKeys[0].isForeignKey());
}

/**
 * @brief Test SqlTable copy and move semantics
 */
TEST_F(SqlTableTest, CopyMoveSemantics) {
    SqlTable original("original_table");
    original.setSchema("test_schema");
    original.setAlias("orig");
    
    auto columns = createTestColumns();
    if (auto metadata = original.metadata()) {
        metadata->setColumns(columns);
        metadata->engine = "InnoDB";
        metadata->comment = "Original table";
    }
    
    // Copy constructor
    SqlTable copied(original);
    EXPECT_EQ(copied.name(), original.name());
    EXPECT_EQ(copied.schema(), original.schema());
    EXPECT_EQ(copied.alias(), original.alias());
    EXPECT_EQ(copied.columnCount(), original.columnCount());
    
    if (auto copiedMetadata = copied.metadata()) {
        if (auto originalMetadata = original.metadata()) {
            EXPECT_EQ(copiedMetadata->engine, originalMetadata->engine);
            EXPECT_EQ(copiedMetadata->comment, originalMetadata->comment);
        }
    }
    
    // Move constructor
    SqlTable moved(std::move(original));
    EXPECT_EQ(moved.name(), "original_table");
    EXPECT_EQ(moved.schema(), "test_schema");
    EXPECT_EQ(moved.alias(), "orig");
    EXPECT_EQ(moved.columnCount(), 3);
}

/**
 * @brief Test SqlTable equality and comparison
 */
TEST_F(SqlTableTest, EqualityComparison) {
    SqlTable table1("users");
    SqlTable table2("users");
    SqlTable table3("products");
    
    // Equal tables
    EXPECT_TRUE(table1 == table2);
    EXPECT_FALSE(table1 != table2);
    
    // Different names
    EXPECT_FALSE(table1 == table3);
    EXPECT_TRUE(table1 != table3);
}

/**
 * @brief Test SqlTable with complex schema
 */
TEST_F(SqlTableTest, ComplexSchema) {
    SqlTable table("complex_table");
    
    // Create complex column set
    std::vector<SqlColumn> columns;
    
    // Primary key
    SqlColumn idCol("id", SqlDataType::BigInt);
    idCol.setConstraints(SqlColumnConstraint::PrimaryKey | SqlColumnConstraint::AutoIncrement);
    columns.push_back(idCol);
    
    // Varchar with length
    SqlColumn nameCol("name", SqlDataType::Varchar);
    nameCol.setLength(255);
    nameCol.setConstraints(SqlColumnConstraint::NotNull);
    columns.push_back(nameCol);
    
    // Decimal with precision and scale
    SqlColumn priceCol("price", SqlDataType::Decimal);
    priceCol.setPrecisionScale(10, 2);
    priceCol.setDefaultValue(Data(0.00));
    columns.push_back(priceCol);
    
    // DateTime with default
    SqlColumn createdCol("created_at", SqlDataType::DateTime);
    createdCol.setConstraints(SqlColumnConstraint::NotNull);
    columns.push_back(createdCol);
    
    // Boolean with default
    SqlColumn activeCol("active", SqlDataType::Boolean);
    activeCol.setDefaultValue(Data(true));
    columns.push_back(activeCol);
    
    if (auto metadata = table.metadata()) {
        metadata->setColumns(columns);
        metadata->schema = "inventory";
        metadata->engine = "InnoDB";
        metadata->charset = "utf8mb4";
        metadata->collation = "utf8mb4_unicode_ci";
        metadata->comment = "Complex table with various column types";
    }
    
    // Verify complex schema
    EXPECT_EQ(table.columnCount(), 5);
    EXPECT_EQ(table.schema(), "inventory");
    
    // Test specific columns
    auto priceColumn = table.column("price");
    EXPECT_TRUE(priceColumn.has_value());
    EXPECT_EQ(priceColumn->dataType(), SqlDataType::Decimal);
    EXPECT_EQ(priceColumn->precision(), 10);
    EXPECT_EQ(priceColumn->scale(), 2);
    
    auto activeColumn = table.column("active");
    EXPECT_TRUE(activeColumn.has_value());
    EXPECT_EQ(activeColumn->dataType(), SqlDataType::Boolean);
    EXPECT_TRUE(activeColumn->hasDefaultValue());
}

/**
 * @brief Test SqlTable edge cases
 */
TEST_F(SqlTableTest, EdgeCases) {
    // Empty table name
    SqlTable emptyTable("");
    EXPECT_FALSE(emptyTable.isValid());
    
    // Table with special characters in name
    SqlTable specialTable("table_with_123");
    EXPECT_TRUE(specialTable.isValid());
    EXPECT_EQ(specialTable.name(), "table_with_123");
    
    // Table with very long name
    std::string longName(255, 'a');
    SqlTable longNameTable(longName);
    EXPECT_TRUE(longNameTable.isValid());
    EXPECT_EQ(longNameTable.name(), longName);
    
    // Table with no columns but valid name
    SqlTable noColumnsTable("empty_table");
    EXPECT_TRUE(noColumnsTable.isValid());
    EXPECT_EQ(noColumnsTable.columnCount(), 0);
}
