^E:\PROJECTS\CODE\AI AGENT\DATABASE_V13\BUILD\_DEPS\GOOGLETEST-SUBBUILD\CMAKEFILES\4880599ACDF54C936C249B334454B1D3\GENERATE.STAMP.RULE
setlocal
"C:\Program Files\CMake\bin\cmake.exe" "-SE:/Projects/Code/AI Agent/database_v13/build/_deps/googletest-subbuild" "-BE:/Projects/Code/AI Agent/database_v13/build/_deps/googletest-subbuild" --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file "E:/Projects/Code/AI Agent/database_v13/build/_deps/googletest-subbuild/googletest-populate.sln"
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
