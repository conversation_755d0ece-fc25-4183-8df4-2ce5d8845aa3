/**
 * @file test_sql_row.cpp
 * @brief Unit tests for SqlRow class
 */

#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include "test_utils.h"
#include "sql/sql_row.h"

using namespace database;
using namespace database::test;
using ::testing::_;
using ::testing::Return;

/**
 * @brief Test fixture for SqlRow tests
 */
class SqlRowTest : public SqlObjectTestBase {
protected:
    void SetUp() override {
        SqlObjectTestBase::SetUp();
    }
};

/**
 * @brief Test SqlRow default constructor
 */
TEST_F(SqlRowTest, DefaultConstructor) {
    SqlRow row;
    
    EXPECT_TRUE(row.name().empty());
    EXPECT_EQ(row.objectType(), SqlObjectType::Row);
    EXPECT_FALSE(row.isValid());
    EXPECT_EQ(row.columnCount(), 0);
    EXPECT_TRUE(row.isEmpty());
}

/**
 * @brief Test SqlRow constructor with name
 */
TEST_F(SqlRowTest, ConstructorWithName) {
    const std::string rowName = "test_row";
    SqlRow row(rowName);
    
    EXPECT_EQ(row.name(), rowName);
    EXPECT_EQ(row.objectType(), SqlObjectType::Row);
    EXPECT_TRUE(row.isValid());
    EXPECT_EQ(row.columnCount(), 0);
    EXPECT_TRUE(row.isEmpty());
}

/**
 * @brief Test SqlRow value management
 */
TEST_F(SqlRowTest, ValueManagement) {
    SqlRow row("test_row");
    
    // Initially empty
    EXPECT_EQ(row.columnCount(), 0);
    EXPECT_TRUE(row.isEmpty());
    EXPECT_FALSE(row.hasColumn("id"));
    
    // Set integer value
    row.setValue("id", Data(42));
    EXPECT_EQ(row.columnCount(), 1);
    EXPECT_FALSE(row.isEmpty());
    EXPECT_TRUE(row.hasColumn("id"));
    
    auto idValue = row.value("id");
    EXPECT_TRUE(idValue.has_value());
    EXPECT_EQ(idValue->toInt(), 42);
    
    // Set string value
    row.setValue("name", Data(std::string("John Doe")));
    EXPECT_EQ(row.columnCount(), 2);
    EXPECT_TRUE(row.hasColumn("name"));
    
    auto nameValue = row.value("name");
    EXPECT_TRUE(nameValue.has_value());
    EXPECT_EQ(nameValue->toString(), "John Doe");
    
    // Set double value
    row.setValue("salary", Data(50000.50));
    EXPECT_EQ(row.columnCount(), 3);
    EXPECT_TRUE(row.hasColumn("salary"));
    
    auto salaryValue = row.value("salary");
    EXPECT_TRUE(salaryValue.has_value());
    EXPECT_DOUBLE_EQ(salaryValue->toDouble(), 50000.50);
    
    // Set boolean value
    row.setValue("active", Data(true));
    EXPECT_EQ(row.columnCount(), 4);
    EXPECT_TRUE(row.hasColumn("active"));
    
    auto activeValue = row.value("active");
    EXPECT_TRUE(activeValue.has_value());
    EXPECT_TRUE(activeValue->toBool());
}

/**
 * @brief Test SqlRow value retrieval
 */
TEST_F(SqlRowTest, ValueRetrieval) {
    SqlRow row("test_row");
    
    // Set test data
    row.setValue("id", Data(1));
    row.setValue("name", Data(std::string("Test User")));
    row.setValue("age", Data(25));
    row.setValue("height", Data(175.5));
    row.setValue("active", Data(true));
    
    // Test value retrieval by column name
    EXPECT_EQ(row.value("id")->toInt(), 1);
    EXPECT_EQ(row.value("name")->toString(), "Test User");
    EXPECT_EQ(row.value("age")->toInt(), 25);
    EXPECT_DOUBLE_EQ(row.value("height")->toDouble(), 175.5);
    EXPECT_TRUE(row.value("active")->toBool());
    
    // Test non-existent column
    auto nonExistent = row.value("non_existent");
    EXPECT_FALSE(nonExistent.has_value());
    
    // Test value retrieval by index
    EXPECT_EQ(row.valueAt(0)->toInt(), 1);
    EXPECT_EQ(row.valueAt(1)->toString(), "Test User");
    EXPECT_EQ(row.valueAt(2)->toInt(), 25);
    
    // Test out-of-bounds index
    auto outOfBounds = row.valueAt(10);
    EXPECT_FALSE(outOfBounds.has_value());
}

/**
 * @brief Test SqlRow column management
 */
TEST_F(SqlRowTest, ColumnManagement) {
    SqlRow row("test_row");
    
    // Add columns with values
    row.setValue("id", Data(1));
    row.setValue("name", Data(std::string("John")));
    row.setValue("email", Data(std::string("<EMAIL>")));
    
    // Test column names
    auto columnNames = row.columnNames();
    EXPECT_EQ(columnNames.size(), 3);
    EXPECT_TRUE(std::find(columnNames.begin(), columnNames.end(), "id") != columnNames.end());
    EXPECT_TRUE(std::find(columnNames.begin(), columnNames.end(), "name") != columnNames.end());
    EXPECT_TRUE(std::find(columnNames.begin(), columnNames.end(), "email") != columnNames.end());
    
    // Test column existence
    EXPECT_TRUE(row.hasColumn("id"));
    EXPECT_TRUE(row.hasColumn("name"));
    EXPECT_TRUE(row.hasColumn("email"));
    EXPECT_FALSE(row.hasColumn("age"));
    
    // Remove column
    row.removeColumn("email");
    EXPECT_EQ(row.columnCount(), 2);
    EXPECT_FALSE(row.hasColumn("email"));
    EXPECT_FALSE(row.value("email").has_value());
    
    // Clear all columns
    row.clear();
    EXPECT_EQ(row.columnCount(), 0);
    EXPECT_TRUE(row.isEmpty());
    EXPECT_TRUE(row.columnNames().empty());
}

/**
 * @brief Test SqlRow table name management
 */
TEST_F(SqlRowTest, TableNameManagement) {
    SqlRow row("test_row");
    
    // Default table name should be empty
    EXPECT_TRUE(row.tableName().empty());
    
    // Set table name
    row.setTableName("users");
    EXPECT_EQ(row.tableName(), "users");
    
    // Change table name
    row.setTableName("customers");
    EXPECT_EQ(row.tableName(), "customers");
}

/**
 * @brief Test SqlRow SQL generation
 */
TEST_F(SqlRowTest, SqlGeneration) {
    SqlRow row("test_row");
    row.setValue("id", Data(1));
    row.setValue("name", Data(std::string("John Doe")));
    row.setValue("email", Data(std::string("<EMAIL>")));
    row.setValue("age", Data(30));
    
    // Test basic SQL generation
    std::string sql = row.toSql();
    EXPECT_FALSE(sql.empty());
    
    // Test INSERT statement generation
    std::string insertSql = row.toInsertStatement("users");
    EXPECT_FALSE(insertSql.empty());
    EXPECT_NE(insertSql.find("INSERT"), std::string::npos);
    EXPECT_NE(insertSql.find("users"), std::string::npos);
    EXPECT_NE(insertSql.find("VALUES"), std::string::npos);
    
    // Test UPDATE statement generation
    std::string updateSql = row.toUpdateStatement("id = 1", "users");
    EXPECT_FALSE(updateSql.empty());
    EXPECT_NE(updateSql.find("UPDATE"), std::string::npos);
    EXPECT_NE(updateSql.find("users"), std::string::npos);
    EXPECT_NE(updateSql.find("SET"), std::string::npos);
    EXPECT_NE(updateSql.find("WHERE"), std::string::npos);
    
    // Test DELETE statement generation
    std::string deleteSql = row.toDeleteStatement("id = 1", "users");
    EXPECT_FALSE(deleteSql.empty());
    EXPECT_NE(deleteSql.find("DELETE"), std::string::npos);
    EXPECT_NE(deleteSql.find("users"), std::string::npos);
    EXPECT_NE(deleteSql.find("WHERE"), std::string::npos);
}

/**
 * @brief Test SqlRow JSON serialization
 */
TEST_F(SqlRowTest, JsonSerialization) {
    SqlRow row("test_row");
    row.setValue("id", Data(1));
    row.setValue("name", Data(std::string("John Doe")));
    row.setValue("active", Data(true));
    row.setValue("salary", Data(50000.0));
    
    std::string json = row.toJson();
    EXPECT_FALSE(json.empty());
    EXPECT_NE(json.find("id"), std::string::npos);
    EXPECT_NE(json.find("name"), std::string::npos);
    EXPECT_NE(json.find("John Doe"), std::string::npos);
    EXPECT_NE(json.find("active"), std::string::npos);
    EXPECT_NE(json.find("salary"), std::string::npos);
}

/**
 * @brief Test SqlRow XML serialization
 */
TEST_F(SqlRowTest, XmlSerialization) {
    SqlRow row("test_row");
    row.setValue("id", Data(1));
    row.setValue("name", Data(std::string("John Doe")));
    row.setValue("email", Data(std::string("<EMAIL>")));
    
    std::string xml = row.toXml();
    EXPECT_FALSE(xml.empty());
    EXPECT_NE(xml.find("id"), std::string::npos);
    EXPECT_NE(xml.find("name"), std::string::npos);
    EXPECT_NE(xml.find("email"), std::string::npos);
    EXPECT_NE(xml.find("John Doe"), std::string::npos);
}

/**
 * @brief Test SqlRow qualified name
 */
TEST_F(SqlRowTest, QualifiedName) {
    SqlRow row("test_row");
    
    // Without table name
    std::string qualifiedName = row.qualifiedName();
    EXPECT_EQ(qualifiedName, "test_row");
    
    // With table name
    row.setTableName("test_table");
    qualifiedName = row.qualifiedName();
    EXPECT_EQ(qualifiedName, "test_table.test_row");
}

/**
 * @brief Test SqlRow copy and move semantics
 */
TEST_F(SqlRowTest, CopyMoveSemantics) {
    SqlRow original("original_row");
    original.setTableName("test_table");
    original.setValue("id", Data(1));
    original.setValue("name", Data(std::string("Original Name")));
    original.setValue("active", Data(true));
    
    // Copy constructor
    SqlRow copied(original);
    EXPECT_EQ(copied.name(), original.name());
    EXPECT_EQ(copied.tableName(), original.tableName());
    EXPECT_EQ(copied.columnCount(), original.columnCount());
    EXPECT_EQ(copied.value("id")->toInt(), 1);
    EXPECT_EQ(copied.value("name")->toString(), "Original Name");
    EXPECT_TRUE(copied.value("active")->toBool());
    
    // Move constructor
    SqlRow moved(std::move(original));
    EXPECT_EQ(moved.name(), "original_row");
    EXPECT_EQ(moved.tableName(), "test_table");
    EXPECT_EQ(moved.columnCount(), 3);
    EXPECT_EQ(moved.value("id")->toInt(), 1);
}

/**
 * @brief Test SqlRow equality and comparison
 */
TEST_F(SqlRowTest, EqualityComparison) {
    SqlRow row1("test_row");
    row1.setValue("id", Data(1));
    row1.setValue("name", Data(std::string("John")));
    
    SqlRow row2("test_row");
    row2.setValue("id", Data(1));
    row2.setValue("name", Data(std::string("John")));
    
    SqlRow row3("different_row");
    row3.setValue("id", Data(1));
    row3.setValue("name", Data(std::string("John")));
    
    SqlRow row4("test_row");
    row4.setValue("id", Data(2));
    row4.setValue("name", Data(std::string("Jane")));
    
    // Equal rows (same name and type from base class)
    EXPECT_TRUE(row1 == row2);
    EXPECT_FALSE(row1 != row2);
    
    // Different names
    EXPECT_FALSE(row1 == row3);
    EXPECT_TRUE(row1 != row3);
    
    // Same name but different data (base class comparison only)
    EXPECT_TRUE(row1 == row4); // Base class only compares name and type
}

/**
 * @brief Test SqlRow with different data types
 */
TEST_F(SqlRowTest, DifferentDataTypes) {
    SqlRow row("typed_row");
    
    // Test various data types
    row.setValue("int_col", Data(42));
    row.setValue("string_col", Data(std::string("test string")));
    row.setValue("double_col", Data(3.14159));
    row.setValue("bool_col", Data(false));
    row.setValue("null_col", Data());
    
    // Verify types and values
    EXPECT_EQ(row.value("int_col")->toInt(), 42);
    EXPECT_EQ(row.value("string_col")->toString(), "test string");
    EXPECT_DOUBLE_EQ(row.value("double_col")->toDouble(), 3.14159);
    EXPECT_FALSE(row.value("bool_col")->toBool());
    EXPECT_TRUE(row.value("null_col")->isNull());
    
    EXPECT_EQ(row.columnCount(), 5);
}

/**
 * @brief Test SqlRow edge cases
 */
TEST_F(SqlRowTest, EdgeCases) {
    SqlRow row("edge_case_row");
    
    // Empty string value
    row.setValue("empty_string", Data(std::string("")));
    EXPECT_TRUE(row.hasColumn("empty_string"));
    EXPECT_EQ(row.value("empty_string")->toString(), "");
    
    // Zero values
    row.setValue("zero_int", Data(0));
    row.setValue("zero_double", Data(0.0));
    EXPECT_EQ(row.value("zero_int")->toInt(), 0);
    EXPECT_DOUBLE_EQ(row.value("zero_double")->toDouble(), 0.0);
    
    // Large values
    row.setValue("large_int", Data(2147483647)); // Max int
    row.setValue("large_double", Data(1.7976931348623157e+308)); // Large double
    EXPECT_EQ(row.value("large_int")->toInt(), 2147483647);
    EXPECT_GT(row.value("large_double")->toDouble(), 1e+300);
    
    // Special characters in column names
    row.setValue("col_with_underscore", Data(1));
    row.setValue("col-with-dash", Data(2));
    EXPECT_TRUE(row.hasColumn("col_with_underscore"));
    EXPECT_TRUE(row.hasColumn("col-with-dash"));
    
    // Overwrite existing value
    row.setValue("overwrite_test", Data(100));
    EXPECT_EQ(row.value("overwrite_test")->toInt(), 100);
    row.setValue("overwrite_test", Data(200));
    EXPECT_EQ(row.value("overwrite_test")->toInt(), 200);
    EXPECT_EQ(row.columnCount(), 7); // Should not increase count
}
