/**
 * @file test_sql_object.cpp
 * @brief Unit tests for SqlObject base class
 */

#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include "test_utils.h"
#include "sql/sql_object.h"
#include "sql/sql_table.h" // For concrete implementation testing

using namespace database;
using namespace database::test;
using ::testing::_;
using ::testing::Return;
using ::testing::StrictMock;

namespace {

/**
 * @brief Mock SqlObject for testing abstract base class functionality
 */
class MockSqlObject : public SqlObject {
public:
    MockSqlObject() : SqlObject() {}
    explicit MockSqlObject(std::string_view name) : SqlObject(name) {}
    MockSqlObject(std::string_view name, SqlObjectType type) : SqlObject(name, type) {}
    
    MOCK_METHOD(std::string, qualifiedName, (), (const, override));
    MOCK_METHOD(std::string, toSql, (), (const, override));
    MOCK_METHOD(std::shared_ptr<SqlDatabase>, database, (), (const, override));
};

} // anonymous namespace

/**
 * @brief Test fixture for SqlObject tests
 */
class SqlObjectTest : public SqlObjectTestBase {
protected:
    void SetUp() override {
        SqlObjectTestBase::SetUp();
    }
};

/**
 * @brief Test SqlObject default constructor
 */
TEST_F(SqlObjectTest, DefaultConstructor) {
    MockSqlObject obj;
    
    EXPECT_TRUE(obj.name().empty());
    EXPECT_EQ(obj.objectType(), SqlObjectType::Unknown);
    EXPECT_FALSE(obj.isValid());
}

/**
 * @brief Test SqlObject constructor with name
 */
TEST_F(SqlObjectTest, ConstructorWithName) {
    const std::string testName = "test_object";
    MockSqlObject obj(testName);
    
    EXPECT_EQ(obj.name(), testName);
    EXPECT_EQ(obj.objectType(), SqlObjectType::Unknown);
    EXPECT_TRUE(obj.isValid());
}

/**
 * @brief Test SqlObject constructor with name and type
 */
TEST_F(SqlObjectTest, ConstructorWithNameAndType) {
    const std::string testName = "test_table";
    const SqlObjectType testType = SqlObjectType::Table;
    MockSqlObject obj(testName, testType);
    
    EXPECT_EQ(obj.name(), testName);
    EXPECT_EQ(obj.objectType(), testType);
    EXPECT_TRUE(obj.isValid());
}

/**
 * @brief Test SqlObject name setter and getter
 */
TEST_F(SqlObjectTest, NameSetterGetter) {
    MockSqlObject obj;
    
    // Initially empty
    EXPECT_TRUE(obj.name().empty());
    EXPECT_FALSE(obj.isValid());
    
    // Set name
    const std::string testName = "new_name";
    obj.setName(testName);
    
    EXPECT_EQ(obj.name(), testName);
    EXPECT_TRUE(obj.isValid());
    
    // Set same name (should not change)
    obj.setName(testName);
    EXPECT_EQ(obj.name(), testName);
    
    // Set different name
    const std::string newName = "another_name";
    obj.setName(newName);
    EXPECT_EQ(obj.name(), newName);
}

/**
 * @brief Test SqlObject validity checks
 */
TEST_F(SqlObjectTest, ValidityChecks) {
    MockSqlObject obj;
    
    // Empty name should be invalid
    EXPECT_FALSE(obj.isValid());
    
    // Non-empty name should be valid
    obj.setName("valid_name");
    EXPECT_TRUE(obj.isValid());
    
    // Empty name again should be invalid
    obj.setName("");
    EXPECT_FALSE(obj.isValid());
    
    // Whitespace-only name should be valid (as per current implementation)
    obj.setName("   ");
    EXPECT_TRUE(obj.isValid());
}

/**
 * @brief Test SqlObject equality operators
 */
TEST_F(SqlObjectTest, EqualityOperators) {
    MockSqlObject obj1("test_name", SqlObjectType::Table);
    MockSqlObject obj2("test_name", SqlObjectType::Table);
    MockSqlObject obj3("different_name", SqlObjectType::Table);
    MockSqlObject obj4("test_name", SqlObjectType::Column);
    
    // Equal objects
    EXPECT_TRUE(obj1 == obj2);
    EXPECT_FALSE(obj1 != obj2);
    
    // Different names
    EXPECT_FALSE(obj1 == obj3);
    EXPECT_TRUE(obj1 != obj3);
    
    // Different types
    EXPECT_FALSE(obj1 == obj4);
    EXPECT_TRUE(obj1 != obj4);
}

/**
 * @brief Test SqlObject comparison operators
 */
TEST_F(SqlObjectTest, ComparisonOperators) {
    MockSqlObject obj1("aaa", SqlObjectType::Table);
    MockSqlObject obj2("bbb", SqlObjectType::Table);
    MockSqlObject obj3("aaa", SqlObjectType::Column);
    MockSqlObject obj4("aaa", SqlObjectType::Table);
    
    // Name-based comparison
    EXPECT_TRUE(obj1 < obj2);
    EXPECT_FALSE(obj2 < obj1);
    
    // Type-based comparison when names are equal
    EXPECT_TRUE(obj3 < obj1); // Column < Table
    
    // Equal objects
    EXPECT_FALSE(obj1 < obj4);
    EXPECT_FALSE(obj4 < obj1);
}

/**
 * @brief Test SqlObject hash function
 */
TEST_F(SqlObjectTest, HashFunction) {
    MockSqlObject obj1("test_name", SqlObjectType::Table);
    MockSqlObject obj2("test_name", SqlObjectType::Table);
    MockSqlObject obj3("different_name", SqlObjectType::Table);
    MockSqlObject obj4("test_name", SqlObjectType::Column);
    
    // Equal objects should have equal hashes
    EXPECT_EQ(obj1.hash(), obj2.hash());
    
    // Different objects should likely have different hashes
    EXPECT_NE(obj1.hash(), obj3.hash());
    EXPECT_NE(obj1.hash(), obj4.hash());
}

/**
 * @brief Test SqlObject copy constructor and assignment
 */
TEST_F(SqlObjectTest, CopySemantics) {
    MockSqlObject original("original_name", SqlObjectType::Table);
    
    // Copy constructor
    MockSqlObject copied(original);
    EXPECT_EQ(copied.name(), original.name());
    EXPECT_EQ(copied.objectType(), original.objectType());
    EXPECT_EQ(copied == original, true);
    
    // Copy assignment
    MockSqlObject assigned;
    assigned = original;
    EXPECT_EQ(assigned.name(), original.name());
    EXPECT_EQ(assigned.objectType(), original.objectType());
    EXPECT_EQ(assigned == original, true);
}

/**
 * @brief Test SqlObject move constructor and assignment
 */
TEST_F(SqlObjectTest, MoveSemantics) {
    const std::string originalName = "movable_name";
    const SqlObjectType originalType = SqlObjectType::Index;
    
    // Move constructor
    MockSqlObject original(originalName, originalType);
    MockSqlObject moved(std::move(original));
    
    EXPECT_EQ(moved.name(), originalName);
    EXPECT_EQ(moved.objectType(), originalType);
    
    // Move assignment
    MockSqlObject original2(originalName, originalType);
    MockSqlObject moveAssigned;
    moveAssigned = std::move(original2);
    
    EXPECT_EQ(moveAssigned.name(), originalName);
    EXPECT_EQ(moveAssigned.objectType(), originalType);
}

/**
 * @brief Test SqlObject with concrete implementation (SqlTable)
 */
TEST_F(SqlObjectTest, ConcreteImplementation) {
    SqlTable table("test_table");
    
    // Test base class functionality through concrete implementation
    EXPECT_EQ(table.name(), "test_table");
    EXPECT_EQ(table.objectType(), SqlObjectType::Table);
    EXPECT_TRUE(table.isValid());
    
    // Test polymorphic behavior
    SqlObject* basePtr = &table;
    EXPECT_EQ(basePtr->name(), "test_table");
    EXPECT_EQ(basePtr->objectType(), SqlObjectType::Table);
    EXPECT_TRUE(basePtr->isValid());
    
    // Test virtual methods are called correctly
    EXPECT_FALSE(basePtr->qualifiedName().empty());
    EXPECT_FALSE(basePtr->toSql().empty());
}

/**
 * @brief Test SqlObject type setting (protected method)
 */
TEST_F(SqlObjectTest, ObjectTypeManagement) {
    // Create a derived class to test protected functionality
    class TestSqlObject : public SqlObject {
    public:
        TestSqlObject() : SqlObject() {}
        
        void setTestType(SqlObjectType type) {
            setObjectType(type);
        }
        
        std::string qualifiedName() const override { return name(); }
        std::string toSql() const override { return "TEST SQL"; }
        std::shared_ptr<SqlDatabase> database() const override { return nullptr; }
    };
    
    TestSqlObject obj;
    EXPECT_EQ(obj.objectType(), SqlObjectType::Unknown);
    
    obj.setTestType(SqlObjectType::View);
    EXPECT_EQ(obj.objectType(), SqlObjectType::View);
    
    obj.setTestType(SqlObjectType::Procedure);
    EXPECT_EQ(obj.objectType(), SqlObjectType::Procedure);
}

/**
 * @brief Parameterized test for different object types
 */
class SqlObjectTypeTest : public SqlObjectTest, 
                         public ::testing::WithParamInterface<SqlObjectType> {
};

TEST_P(SqlObjectTypeTest, ObjectTypeHandling) {
    SqlObjectType testType = GetParam();
    MockSqlObject obj("test_object", testType);
    
    EXPECT_EQ(obj.objectType(), testType);
    EXPECT_TRUE(obj.isValid());
    EXPECT_EQ(obj.name(), "test_object");
}

INSTANTIATE_TEST_SUITE_P(
    AllObjectTypes,
    SqlObjectTypeTest,
    ::testing::Values(
        SqlObjectType::Unknown,
        SqlObjectType::Table,
        SqlObjectType::Column,
        SqlObjectType::Index,
        SqlObjectType::View,
        SqlObjectType::Row,
        SqlObjectType::Schema,
        SqlObjectType::Trigger,
        SqlObjectType::Procedure,
        SqlObjectType::Function
    )
);
