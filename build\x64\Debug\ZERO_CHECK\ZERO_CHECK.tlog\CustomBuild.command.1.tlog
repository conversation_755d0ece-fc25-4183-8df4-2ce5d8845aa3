^E:\PROJECTS\CODE\AI AGENT\DATABASE_V13\BUILD\CMAKEFILES\C1F2BDE2D259102B42F38FF204F9B324\GENERATE.STAMP.RULE
setlocal
"C:\Program Files\CMake\bin\cmake.exe" "-SE:/Projects/Code/AI Agent/database_v13" "-BE:/Projects/Code/AI Agent/database_v13/build" --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file "E:/Projects/Code/AI Agent/database_v13/build/database_test.sln"
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
