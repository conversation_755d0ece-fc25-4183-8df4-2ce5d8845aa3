/**
 * @file test_utils.h
 * @brief Common testing utilities and helpers for SQL object hierarchy tests
 */

#ifndef TEST_UTILS_H
#define TEST_UTILS_H

#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include <memory>
#include <string>
#include <vector>
#include <random>

// Include SQL object headers
#include "sql/sql_object.h"
#include "sql/sql_column.h"
#include "sql/sql_index.h"
#include "sql/sql_row.h"
#include "sql/sql_table.h"
#include "sql/sql_view.h"
#include "sql_database.h"
#include "sql_types.h"

namespace database {
namespace test {

/**
 * @brief Test fixture base class for SQL object tests
 */
class SqlObjectTestBase : public ::testing::Test {
protected:
    void SetUp() override {
        // Common setup for all SQL object tests
    }
    
    void TearDown() override {
        // Common cleanup for all SQL object tests
    }
    
    /**
     * @brief Create a test database connection (mock or in-memory SQLite)
     */
    std::shared_ptr<SqlDatabase> createTestDatabase() {
        // For now, return nullptr - can be extended with actual database connection
        return nullptr;
    }
};

/**
 * @brief Test data generator utilities
 */
class TestDataGenerator {
public:
    /**
     * @brief Generate random string of specified length
     */
    static std::string randomString(size_t length = 10) {
        static const char charset[] = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
        static std::random_device rd;
        static std::mt19937 gen(rd());
        static std::uniform_int_distribution<> dis(0, sizeof(charset) - 2);
        
        std::string result;
        result.reserve(length);
        for (size_t i = 0; i < length; ++i) {
            result += charset[dis(gen)];
        }
        return result;
    }
    
    /**
     * @brief Generate random integer within range
     */
    static int randomInt(int min = 0, int max = 1000) {
        static std::random_device rd;
        static std::mt19937 gen(rd());
        std::uniform_int_distribution<> dis(min, max);
        return dis(gen);
    }
    
    /**
     * @brief Create test column with random properties
     */
    static SqlColumn createTestColumn(const std::string& name = "") {
        std::string columnName = name.empty() ? "test_col_" + randomString(5) : name;
        SqlColumn column(columnName, SqlDataType::Varchar);
        column.setLength(randomInt(10, 255));
        return column;
    }
    
    /**
     * @brief Create test row with sample data
     */
    static SqlRow createTestRow(const std::string& name = "") {
        std::string rowName = name.empty() ? "test_row_" + randomString(5) : name;
        SqlRow row(rowName);
        
        row.setValue("id", Data(randomInt(1, 1000)));
        row.setValue("name", Data(randomString(10)));
        row.setValue("email", Data(randomString(8) + "@example.com"));
        row.setValue("age", Data(randomInt(18, 80)));
        
        return row;
    }
    
    /**
     * @brief Create test table with sample columns
     */
    static SqlTable createTestTable(const std::string& name = "") {
        std::string tableName = name.empty() ? "test_table_" + randomString(5) : name;
        SqlTable table(tableName);
        
        // Add some standard columns
        std::vector<SqlColumn> columns;
        
        SqlColumn idCol("id", SqlDataType::Integer);
        idCol.setConstraints(SqlColumnConstraint::PrimaryKey | SqlColumnConstraint::AutoIncrement);
        columns.push_back(idCol);
        
        SqlColumn nameCol("name", SqlDataType::Varchar);
        nameCol.setLength(100);
        nameCol.setConstraints(SqlColumnConstraint::NotNull);
        columns.push_back(nameCol);
        
        SqlColumn emailCol("email", SqlDataType::Varchar);
        emailCol.setLength(255);
        emailCol.setConstraints(SqlColumnConstraint::Unique);
        columns.push_back(emailCol);
        
        if (auto metadata = table.metadata()) {
            metadata->setColumns(columns);
        }
        
        return table;
    }
    
    /**
     * @brief Create test index with sample configuration
     */
    static SqlIndex createTestIndex(const std::string& name = "", const std::string& tableName = "test_table") {
        std::string indexName = name.empty() ? "idx_" + randomString(5) : name;
        SqlIndex index(indexName, SqlIndexType::Normal);
        
        if (auto metadata = index.metadata()) {
            metadata->tableName = tableName;
        }
        
        index.addColumn("name");
        return index;
    }
    
    /**
     * @brief Create test view with sample query
     */
    static SqlView createTestView(const std::string& name = "") {
        std::string viewName = name.empty() ? "view_" + randomString(5) : name;
        SqlView view(viewName);
        
        std::string query = "SELECT id, name, email FROM test_table WHERE active = 1";
        view.setQuery(query);
        
        return view;
    }
};

/**
 * @brief Custom matchers for SQL objects
 */
MATCHER_P(HasName, expectedName, "has name " + std::string(expectedName)) {
    return arg.name() == expectedName;
}

MATCHER_P(HasObjectType, expectedType, "has object type") {
    return arg.objectType() == expectedType;
}

MATCHER(IsValid, "is valid") {
    return arg.isValid();
}

MATCHER(IsNotValid, "is not valid") {
    return !arg.isValid();
}

/**
 * @brief Helper macros for common test assertions
 */
#define EXPECT_SQL_OBJECT_VALID(obj) \
    EXPECT_TRUE((obj).isValid()) << "SQL object should be valid"

#define EXPECT_SQL_OBJECT_INVALID(obj) \
    EXPECT_FALSE((obj).isValid()) << "SQL object should be invalid"

#define EXPECT_SQL_OBJECT_NAME(obj, expected_name) \
    EXPECT_EQ((obj).name(), (expected_name)) << "SQL object name mismatch"

#define EXPECT_SQL_OBJECT_TYPE(obj, expected_type) \
    EXPECT_EQ((obj).objectType(), (expected_type)) << "SQL object type mismatch"

/**
 * @brief Test parameter structures for parameterized tests
 */
struct SqlDataTypeTestParam {
    SqlDataType dataType;
    std::string expectedString;
    bool isNumeric;
    bool isText;
};

struct SqlConstraintTestParam {
    SqlColumnConstraint constraint;
    std::string expectedString;
    bool isRequired;
};

struct SqlIndexTypeTestParam {
    SqlIndexType indexType;
    std::string expectedString;
    bool isUnique;
};

/**
 * @brief Common test data sets
 */
class TestDataSets {
public:
    static std::vector<SqlDataTypeTestParam> getAllDataTypes() {
        return {
            {SqlDataType::Integer, "INTEGER", true, false},
            {SqlDataType::Varchar, "VARCHAR", false, true},
            {SqlDataType::Text, "TEXT", false, true},
            {SqlDataType::Real, "REAL", true, false},
            {SqlDataType::Boolean, "BOOLEAN", false, false},
            {SqlDataType::Date, "DATE", false, false},
            {SqlDataType::DateTime, "DATETIME", false, false}
        };
    }
    
    static std::vector<SqlConstraintTestParam> getAllConstraints() {
        return {
            {SqlColumnConstraint::NotNull, "NOT NULL", true},
            {SqlColumnConstraint::Unique, "UNIQUE", false},
            {SqlColumnConstraint::PrimaryKey, "PRIMARY KEY", true},
            {SqlColumnConstraint::AutoIncrement, "AUTOINCREMENT", false}
        };
    }
    
    static std::vector<SqlIndexTypeTestParam> getAllIndexTypes() {
        return {
            {SqlIndexType::Primary, "PRIMARY", true},
            {SqlIndexType::Unique, "UNIQUE", true},
            {SqlIndexType::Normal, "INDEX", false},
            {SqlIndexType::Hash, "HASH", false}
        };
    }
};

} // namespace test
} // namespace database

#endif // TEST_UTILS_H
