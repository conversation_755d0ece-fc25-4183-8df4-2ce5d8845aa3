@echo off
REM Build and Test Script for SQL Object Hierarchy
REM This script builds the project and runs all tests

echo ========================================
echo SQL Object Hierarchy Build and Test
echo ========================================

REM Create build directory if it doesn't exist
if not exist "build" (
    echo Creating build directory...
    mkdir build
)

REM Navigate to build directory
cd build

echo.
echo ========================================
echo Configuring with CMake...
echo ========================================
cmake .. -DCMAKE_BUILD_TYPE=Debug

if %ERRORLEVEL% neq 0 (
    echo CMake configuration failed!
    pause
    exit /b 1
)

echo.
echo ========================================
echo Building project...
echo ========================================
cmake --build . --config Debug

if %ERRORLEVEL% neq 0 (
    echo Build failed!
    pause
    exit /b 1
)

echo.
echo ========================================
echo Running all tests...
echo ========================================
if exist "tests\Debug\sql_objects_tests.exe" (
    tests\Debug\sql_objects_tests.exe
) else if exist "tests\sql_objects_tests.exe" (
    tests\sql_objects_tests.exe
) else (
    echo Test executable not found!
    pause
    exit /b 1
)

if %ERRORLEVEL% neq 0 (
    echo Some tests failed!
    pause
    exit /b 1
)

echo.
echo ========================================
echo Running example program...
echo ========================================
if exist "bin\Debug\sql_objects_example.exe" (
    bin\Debug\sql_objects_example.exe
) else if exist "bin\sql_objects_example.exe" (
    bin\sql_objects_example.exe
) else (
    echo Example executable not found, but tests passed!
)

echo.
echo ========================================
echo All tests completed successfully!
echo ========================================
pause
