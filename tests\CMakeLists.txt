# Tests CMakeLists.txt for SQL Object Hierarchy Unit Tests

cmake_minimum_required(VERSION 3.14)

# Include GoogleTest
find_package(GTest REQUIRED)

# Include directories for tests
include_directories(
    ${CMAKE_CURRENT_SOURCE_DIR}/../
    ${CMAKE_CURRENT_SOURCE_DIR}/../sql
    ${CMAKE_CURRENT_SOURCE_DIR}/../types
    ${CMAKE_CURRENT_SOURCE_DIR}/../utils
    ${CMAKE_CURRENT_SOURCE_DIR}/../connection
    ${CMAKE_CURRENT_SOURCE_DIR}/../driver
    ${CMAKE_CURRENT_SOURCE_DIR}/../exception
)

# Test source files
set(TEST_SOURCES
    test_sql_object.cpp
    test_sql_column.cpp
    test_sql_index.cpp
    test_sql_row.cpp
    test_sql_table.cpp
    test_sql_view.cpp
)

# Create test executable
add_executable(sql_objects_tests ${TEST_SOURCES})

# Link libraries
target_link_libraries(sql_objects_tests
    PRIVATE
    database_lib
    GTest::gtest
    GTest::gtest_main
    GTest::gmock
    GTest::gmock_main
)

# Set C++ standard for tests
target_compile_features(sql_objects_tests PRIVATE cxx_std_20)

# Add compiler flags for tests
if(MSVC)
    target_compile_options(sql_objects_tests PRIVATE /W4 /permissive- /Zc:__cplusplus /EHsc)
else()
    target_compile_options(sql_objects_tests PRIVATE -Wall -Wextra -Wpedantic)
endif()

# Copy SQLite DLL to test output directory on Windows
if(WIN32)
    add_custom_command(TARGET sql_objects_tests POST_BUILD
        COMMAND ${CMAKE_COMMAND} -E copy_if_different
        "${CMAKE_CURRENT_SOURCE_DIR}/../third_party/sqlite/sqlite3.dll"
        $<TARGET_FILE_DIR:sql_objects_tests>
    )
endif()

# Register tests with CTest
include(GoogleTest)
gtest_discover_tests(sql_objects_tests
    WORKING_DIRECTORY ${CMAKE_CURRENT_BINARY_DIR}
    PROPERTIES VS_DEBUGGER_WORKING_DIRECTORY "${CMAKE_CURRENT_BINARY_DIR}"
)

# Add individual test targets for easier debugging
add_test(NAME SqlObjectTests COMMAND sql_objects_tests --gtest_filter="SqlObjectTest.*")
add_test(NAME SqlColumnTests COMMAND sql_objects_tests --gtest_filter="SqlColumnTest.*")
add_test(NAME SqlIndexTests COMMAND sql_objects_tests --gtest_filter="SqlIndexTest.*")
add_test(NAME SqlRowTests COMMAND sql_objects_tests --gtest_filter="SqlRowTest.*")
add_test(NAME SqlTableTests COMMAND sql_objects_tests --gtest_filter="SqlTableTest.*")
add_test(NAME SqlViewTests COMMAND sql_objects_tests --gtest_filter="SqlViewTest.*")
