﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ALL_BUILD", "ALL_BUILD.vcxproj", "{0F5213AF-1307-3E65-A294-F162C3F298A1}"
	ProjectSection(ProjectDependencies) = postProject
		{742F3656-6D38-381A-A919-6A23E14D05C4} = {742F3656-6D38-381A-A919-6A23E14D05C4}
		{04BD33EA-F433-3312-8974-760B97926F95} = {04BD33EA-F433-3312-8974-760B97926F95}
		{44D02237-3679-3A48-ACC1-20335C1DC628} = {44D02237-3679-3A48-ACC1-20335C1DC628}
		{7A959FA2-8EB6-3A0A-AD65-CDC82D5B90DC} = {7A959FA2-8EB6-3A0A-AD65-CDC82D5B90DC}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "RUN_TESTS", "RUN_TESTS.vcxproj", "{D8531FBD-A52C-39CF-B86B-E79D5FE9667E}"
	ProjectSection(ProjectDependencies) = postProject
		{742F3656-6D38-381A-A919-6A23E14D05C4} = {742F3656-6D38-381A-A919-6A23E14D05C4}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ZERO_CHECK", "ZERO_CHECK.vcxproj", "{742F3656-6D38-381A-A919-6A23E14D05C4}"
	ProjectSection(ProjectDependencies) = postProject
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "database_lib", "database_lib.vcxproj", "{04BD33EA-F433-3312-8974-760B97926F95}"
	ProjectSection(ProjectDependencies) = postProject
		{742F3656-6D38-381A-A919-6A23E14D05C4} = {742F3656-6D38-381A-A919-6A23E14D05C4}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "sql_objects_example", "sql_objects_example.vcxproj", "{44D02237-3679-3A48-ACC1-20335C1DC628}"
	ProjectSection(ProjectDependencies) = postProject
		{742F3656-6D38-381A-A919-6A23E14D05C4} = {742F3656-6D38-381A-A919-6A23E14D05C4}
		{04BD33EA-F433-3312-8974-760B97926F95} = {04BD33EA-F433-3312-8974-760B97926F95}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "sql_objects_tests", "tests\sql_objects_tests.vcxproj", "{7A959FA2-8EB6-3A0A-AD65-CDC82D5B90DC}"
	ProjectSection(ProjectDependencies) = postProject
		{742F3656-6D38-381A-A919-6A23E14D05C4} = {742F3656-6D38-381A-A919-6A23E14D05C4}
		{04BD33EA-F433-3312-8974-760B97926F95} = {04BD33EA-F433-3312-8974-760B97926F95}
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|x64 = Debug|x64
		Release|x64 = Release|x64
		MinSizeRel|x64 = MinSizeRel|x64
		RelWithDebInfo|x64 = RelWithDebInfo|x64
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{0F5213AF-1307-3E65-A294-F162C3F298A1}.Debug|x64.ActiveCfg = Debug|x64
		{0F5213AF-1307-3E65-A294-F162C3F298A1}.Release|x64.ActiveCfg = Release|x64
		{0F5213AF-1307-3E65-A294-F162C3F298A1}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{0F5213AF-1307-3E65-A294-F162C3F298A1}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{D8531FBD-A52C-39CF-B86B-E79D5FE9667E}.Debug|x64.ActiveCfg = Debug|x64
		{D8531FBD-A52C-39CF-B86B-E79D5FE9667E}.Release|x64.ActiveCfg = Release|x64
		{D8531FBD-A52C-39CF-B86B-E79D5FE9667E}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{D8531FBD-A52C-39CF-B86B-E79D5FE9667E}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{742F3656-6D38-381A-A919-6A23E14D05C4}.Debug|x64.ActiveCfg = Debug|x64
		{742F3656-6D38-381A-A919-6A23E14D05C4}.Debug|x64.Build.0 = Debug|x64
		{742F3656-6D38-381A-A919-6A23E14D05C4}.Release|x64.ActiveCfg = Release|x64
		{742F3656-6D38-381A-A919-6A23E14D05C4}.Release|x64.Build.0 = Release|x64
		{742F3656-6D38-381A-A919-6A23E14D05C4}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{742F3656-6D38-381A-A919-6A23E14D05C4}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{742F3656-6D38-381A-A919-6A23E14D05C4}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{742F3656-6D38-381A-A919-6A23E14D05C4}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{04BD33EA-F433-3312-8974-760B97926F95}.Debug|x64.ActiveCfg = Debug|x64
		{04BD33EA-F433-3312-8974-760B97926F95}.Debug|x64.Build.0 = Debug|x64
		{04BD33EA-F433-3312-8974-760B97926F95}.Release|x64.ActiveCfg = Release|x64
		{04BD33EA-F433-3312-8974-760B97926F95}.Release|x64.Build.0 = Release|x64
		{04BD33EA-F433-3312-8974-760B97926F95}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{04BD33EA-F433-3312-8974-760B97926F95}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{04BD33EA-F433-3312-8974-760B97926F95}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{04BD33EA-F433-3312-8974-760B97926F95}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{44D02237-3679-3A48-ACC1-20335C1DC628}.Debug|x64.ActiveCfg = Debug|x64
		{44D02237-3679-3A48-ACC1-20335C1DC628}.Debug|x64.Build.0 = Debug|x64
		{44D02237-3679-3A48-ACC1-20335C1DC628}.Release|x64.ActiveCfg = Release|x64
		{44D02237-3679-3A48-ACC1-20335C1DC628}.Release|x64.Build.0 = Release|x64
		{44D02237-3679-3A48-ACC1-20335C1DC628}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{44D02237-3679-3A48-ACC1-20335C1DC628}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{44D02237-3679-3A48-ACC1-20335C1DC628}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{44D02237-3679-3A48-ACC1-20335C1DC628}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{7A959FA2-8EB6-3A0A-AD65-CDC82D5B90DC}.Debug|x64.ActiveCfg = Debug|x64
		{7A959FA2-8EB6-3A0A-AD65-CDC82D5B90DC}.Debug|x64.Build.0 = Debug|x64
		{7A959FA2-8EB6-3A0A-AD65-CDC82D5B90DC}.Release|x64.ActiveCfg = Release|x64
		{7A959FA2-8EB6-3A0A-AD65-CDC82D5B90DC}.Release|x64.Build.0 = Release|x64
		{7A959FA2-8EB6-3A0A-AD65-CDC82D5B90DC}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{7A959FA2-8EB6-3A0A-AD65-CDC82D5B90DC}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{7A959FA2-8EB6-3A0A-AD65-CDC82D5B90DC}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{7A959FA2-8EB6-3A0A-AD65-CDC82D5B90DC}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {DF61125E-8E12-3FD5-8D42-14A6FEC23925}
	EndGlobalSection
	GlobalSection(ExtensibilityAddIns) = postSolution
	EndGlobalSection
EndGlobal
