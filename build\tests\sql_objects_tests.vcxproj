﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="MinSizeRel|x64">
      <Configuration>MinSizeRel</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="RelWithDebInfo|x64">
      <Configuration>RelWithDebInfo</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{7A959FA2-8EB6-3A0A-AD65-CDC82D5B90DC}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.22621.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>sql_objects_tests</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">E:\Projects\Code\AI Agent\database_v13\build\bin\Debug\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">sql_objects_tests.dir\Debug\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">sql_objects_tests</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">E:\Projects\Code\AI Agent\database_v13\build\bin\Release\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">sql_objects_tests.dir\Release\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Release|x64'">sql_objects_tests</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Release|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">E:\Projects\Code\AI Agent\database_v13\build\bin\MinSizeRel\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">sql_objects_tests.dir\MinSizeRel\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">sql_objects_tests</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">E:\Projects\Code\AI Agent\database_v13\build\bin\RelWithDebInfo\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">sql_objects_tests.dir\RelWithDebInfo\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">sql_objects_tests</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</GenerateManifest>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>E:\Projects\Code\AI Agent\database_v13;E:\Projects\Code\AI Agent\database_v13\types;E:\Projects\Code\AI Agent\database_v13\utils;E:\Projects\Code\AI Agent\database_v13\tests\..;E:\Projects\Code\AI Agent\database_v13\tests\..\sql;E:\Projects\Code\AI Agent\database_v13\tests\..\types;E:\Projects\Code\AI Agent\database_v13\tests\..\utils;E:\Projects\Code\AI Agent\database_v13\tests\..\connection;E:\Projects\Code\AI Agent\database_v13\tests\..\driver;E:\Projects\Code\AI Agent\database_v13\tests\..\exception;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "E:/Projects/Code/AI Agent/database_v13/third_party/googletest-distribution/include" /Zc:__cplusplus</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <ConformanceMode>true</ConformanceMode>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>Disabled</InlineFunctionExpansion>
      <LanguageStandard>stdcpp20</LanguageStandard>
      <Optimization>Disabled</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level4</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;CMAKE_INTDIR="Debug"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_DEBUG;_WINDOWS;CMAKE_INTDIR=\"Debug\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>E:\Projects\Code\AI Agent\database_v13;E:\Projects\Code\AI Agent\database_v13\types;E:\Projects\Code\AI Agent\database_v13\utils;E:\Projects\Code\AI Agent\database_v13\tests\..;E:\Projects\Code\AI Agent\database_v13\tests\..\sql;E:\Projects\Code\AI Agent\database_v13\tests\..\types;E:\Projects\Code\AI Agent\database_v13\tests\..\utils;E:\Projects\Code\AI Agent\database_v13\tests\..\connection;E:\Projects\Code\AI Agent\database_v13\tests\..\driver;E:\Projects\Code\AI Agent\database_v13\tests\..\exception;E:\Projects\Code\AI Agent\database_v13\third_party\googletest-distribution\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>E:\Projects\Code\AI Agent\database_v13;E:\Projects\Code\AI Agent\database_v13\types;E:\Projects\Code\AI Agent\database_v13\utils;E:\Projects\Code\AI Agent\database_v13\tests\..;E:\Projects\Code\AI Agent\database_v13\tests\..\sql;E:\Projects\Code\AI Agent\database_v13\tests\..\types;E:\Projects\Code\AI Agent\database_v13\tests\..\utils;E:\Projects\Code\AI Agent\database_v13\tests\..\connection;E:\Projects\Code\AI Agent\database_v13\tests\..\driver;E:\Projects\Code\AI Agent\database_v13\tests\..\exception;E:\Projects\Code\AI Agent\database_v13\third_party\googletest-distribution\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PostBuildEvent>
      <Message>	</Message>
      <Command>setlocal
"C:\Program Files\CMake\bin\cmake.exe" -E copy_if_different "E:/Projects/Code/AI Agent/database_v13/tests/../third_party/sqlite/sqlite3.dll" "E:/Projects/Code/AI Agent/database_v13/build/bin/Debug"
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
setlocal
"C:\Program Files\CMake\bin\cmake.exe" -D TEST_TARGET=sql_objects_tests -D "TEST_EXECUTABLE=E:/Projects/Code/AI Agent/database_v13/build/bin/Debug/sql_objects_tests.exe" -D TEST_EXECUTOR= -D "TEST_WORKING_DIR=E:/Projects/Code/AI Agent/database_v13/build/tests" -D TEST_EXTRA_ARGS= -D "TEST_PROPERTIES=VS_DEBUGGER_WORKING_DIRECTORY";"E:/Projects/Code/AI Agent/database_v13/build/tests" -D TEST_PREFIX= -D TEST_SUFFIX= -D TEST_FILTER= -D NO_PRETTY_TYPES=FALSE -D NO_PRETTY_VALUES=FALSE -D TEST_LIST=sql_objects_tests_TESTS -D "CTEST_FILE=E:/Projects/Code/AI Agent/database_v13/build/tests/sql_objects_tests[1]_tests.cmake" -D TEST_DISCOVERY_TIMEOUT=5 -D TEST_XML_OUTPUT_DIR= -P "C:/Program Files/CMake/share/cmake-3.28/Modules/GoogleTestAddTests.cmake"
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PostBuildEvent>
    <Link>
      <AdditionalDependencies>..\lib\Debug\database_lib.lib;E:\Projects\Code\AI Agent\database_v13\third_party\googletest-distribution\lib\gtest.lib;E:\Projects\Code\AI Agent\database_v13\third_party\googletest-distribution\lib\gtest_main.lib;E:\Projects\Code\AI Agent\database_v13\third_party\googletest-distribution\lib\gmock.lib;E:\Projects\Code\AI Agent\database_v13\third_party\googletest-distribution\lib\gmock_main.lib;E:\Projects\Code\AI Agent\database_v13\third_party\sqlite\sqlite3.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>E:/Projects/Code/AI Agent/database_v13/build/lib/Debug/sql_objects_tests.lib</ImportLibrary>
      <ProgramDataBaseFile>E:/Projects/Code/AI Agent/database_v13/build/bin/Debug/sql_objects_tests.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>E:\Projects\Code\AI Agent\database_v13;E:\Projects\Code\AI Agent\database_v13\types;E:\Projects\Code\AI Agent\database_v13\utils;E:\Projects\Code\AI Agent\database_v13\tests\..;E:\Projects\Code\AI Agent\database_v13\tests\..\sql;E:\Projects\Code\AI Agent\database_v13\tests\..\types;E:\Projects\Code\AI Agent\database_v13\tests\..\utils;E:\Projects\Code\AI Agent\database_v13\tests\..\connection;E:\Projects\Code\AI Agent\database_v13\tests\..\driver;E:\Projects\Code\AI Agent\database_v13\tests\..\exception;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "E:/Projects/Code/AI Agent/database_v13/third_party/googletest-distribution/include" /Zc:__cplusplus</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <ConformanceMode>true</ConformanceMode>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <LanguageStandard>stdcpp20</LanguageStandard>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level4</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;CMAKE_INTDIR="Release"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;CMAKE_INTDIR=\"Release\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>E:\Projects\Code\AI Agent\database_v13;E:\Projects\Code\AI Agent\database_v13\types;E:\Projects\Code\AI Agent\database_v13\utils;E:\Projects\Code\AI Agent\database_v13\tests\..;E:\Projects\Code\AI Agent\database_v13\tests\..\sql;E:\Projects\Code\AI Agent\database_v13\tests\..\types;E:\Projects\Code\AI Agent\database_v13\tests\..\utils;E:\Projects\Code\AI Agent\database_v13\tests\..\connection;E:\Projects\Code\AI Agent\database_v13\tests\..\driver;E:\Projects\Code\AI Agent\database_v13\tests\..\exception;E:\Projects\Code\AI Agent\database_v13\third_party\googletest-distribution\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>E:\Projects\Code\AI Agent\database_v13;E:\Projects\Code\AI Agent\database_v13\types;E:\Projects\Code\AI Agent\database_v13\utils;E:\Projects\Code\AI Agent\database_v13\tests\..;E:\Projects\Code\AI Agent\database_v13\tests\..\sql;E:\Projects\Code\AI Agent\database_v13\tests\..\types;E:\Projects\Code\AI Agent\database_v13\tests\..\utils;E:\Projects\Code\AI Agent\database_v13\tests\..\connection;E:\Projects\Code\AI Agent\database_v13\tests\..\driver;E:\Projects\Code\AI Agent\database_v13\tests\..\exception;E:\Projects\Code\AI Agent\database_v13\third_party\googletest-distribution\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PostBuildEvent>
      <Message>	</Message>
      <Command>setlocal
"C:\Program Files\CMake\bin\cmake.exe" -E copy_if_different "E:/Projects/Code/AI Agent/database_v13/tests/../third_party/sqlite/sqlite3.dll" "E:/Projects/Code/AI Agent/database_v13/build/bin/Release"
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
setlocal
"C:\Program Files\CMake\bin\cmake.exe" -D TEST_TARGET=sql_objects_tests -D "TEST_EXECUTABLE=E:/Projects/Code/AI Agent/database_v13/build/bin/Release/sql_objects_tests.exe" -D TEST_EXECUTOR= -D "TEST_WORKING_DIR=E:/Projects/Code/AI Agent/database_v13/build/tests" -D TEST_EXTRA_ARGS= -D "TEST_PROPERTIES=VS_DEBUGGER_WORKING_DIRECTORY";"E:/Projects/Code/AI Agent/database_v13/build/tests" -D TEST_PREFIX= -D TEST_SUFFIX= -D TEST_FILTER= -D NO_PRETTY_TYPES=FALSE -D NO_PRETTY_VALUES=FALSE -D TEST_LIST=sql_objects_tests_TESTS -D "CTEST_FILE=E:/Projects/Code/AI Agent/database_v13/build/tests/sql_objects_tests[1]_tests.cmake" -D TEST_DISCOVERY_TIMEOUT=5 -D TEST_XML_OUTPUT_DIR= -P "C:/Program Files/CMake/share/cmake-3.28/Modules/GoogleTestAddTests.cmake"
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PostBuildEvent>
    <Link>
      <AdditionalDependencies>..\lib\Release\database_lib.lib;E:\Projects\Code\AI Agent\database_v13\third_party\googletest-distribution\lib\gtest.lib;E:\Projects\Code\AI Agent\database_v13\third_party\googletest-distribution\lib\gtest_main.lib;E:\Projects\Code\AI Agent\database_v13\third_party\googletest-distribution\lib\gmock.lib;E:\Projects\Code\AI Agent\database_v13\third_party\googletest-distribution\lib\gmock_main.lib;E:\Projects\Code\AI Agent\database_v13\third_party\sqlite\sqlite3.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>E:/Projects/Code/AI Agent/database_v13/build/lib/Release/sql_objects_tests.lib</ImportLibrary>
      <ProgramDataBaseFile>E:/Projects/Code/AI Agent/database_v13/build/bin/Release/sql_objects_tests.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>E:\Projects\Code\AI Agent\database_v13;E:\Projects\Code\AI Agent\database_v13\types;E:\Projects\Code\AI Agent\database_v13\utils;E:\Projects\Code\AI Agent\database_v13\tests\..;E:\Projects\Code\AI Agent\database_v13\tests\..\sql;E:\Projects\Code\AI Agent\database_v13\tests\..\types;E:\Projects\Code\AI Agent\database_v13\tests\..\utils;E:\Projects\Code\AI Agent\database_v13\tests\..\connection;E:\Projects\Code\AI Agent\database_v13\tests\..\driver;E:\Projects\Code\AI Agent\database_v13\tests\..\exception;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "E:/Projects/Code/AI Agent/database_v13/third_party/googletest-distribution/include" /Zc:__cplusplus</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <ConformanceMode>true</ConformanceMode>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <LanguageStandard>stdcpp20</LanguageStandard>
      <Optimization>MinSpace</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level4</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;CMAKE_INTDIR="MinSizeRel"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;CMAKE_INTDIR=\"MinSizeRel\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>E:\Projects\Code\AI Agent\database_v13;E:\Projects\Code\AI Agent\database_v13\types;E:\Projects\Code\AI Agent\database_v13\utils;E:\Projects\Code\AI Agent\database_v13\tests\..;E:\Projects\Code\AI Agent\database_v13\tests\..\sql;E:\Projects\Code\AI Agent\database_v13\tests\..\types;E:\Projects\Code\AI Agent\database_v13\tests\..\utils;E:\Projects\Code\AI Agent\database_v13\tests\..\connection;E:\Projects\Code\AI Agent\database_v13\tests\..\driver;E:\Projects\Code\AI Agent\database_v13\tests\..\exception;E:\Projects\Code\AI Agent\database_v13\third_party\googletest-distribution\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>E:\Projects\Code\AI Agent\database_v13;E:\Projects\Code\AI Agent\database_v13\types;E:\Projects\Code\AI Agent\database_v13\utils;E:\Projects\Code\AI Agent\database_v13\tests\..;E:\Projects\Code\AI Agent\database_v13\tests\..\sql;E:\Projects\Code\AI Agent\database_v13\tests\..\types;E:\Projects\Code\AI Agent\database_v13\tests\..\utils;E:\Projects\Code\AI Agent\database_v13\tests\..\connection;E:\Projects\Code\AI Agent\database_v13\tests\..\driver;E:\Projects\Code\AI Agent\database_v13\tests\..\exception;E:\Projects\Code\AI Agent\database_v13\third_party\googletest-distribution\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PostBuildEvent>
      <Message>	</Message>
      <Command>setlocal
"C:\Program Files\CMake\bin\cmake.exe" -E copy_if_different "E:/Projects/Code/AI Agent/database_v13/tests/../third_party/sqlite/sqlite3.dll" "E:/Projects/Code/AI Agent/database_v13/build/bin/MinSizeRel"
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
setlocal
"C:\Program Files\CMake\bin\cmake.exe" -D TEST_TARGET=sql_objects_tests -D "TEST_EXECUTABLE=E:/Projects/Code/AI Agent/database_v13/build/bin/MinSizeRel/sql_objects_tests.exe" -D TEST_EXECUTOR= -D "TEST_WORKING_DIR=E:/Projects/Code/AI Agent/database_v13/build/tests" -D TEST_EXTRA_ARGS= -D "TEST_PROPERTIES=VS_DEBUGGER_WORKING_DIRECTORY";"E:/Projects/Code/AI Agent/database_v13/build/tests" -D TEST_PREFIX= -D TEST_SUFFIX= -D TEST_FILTER= -D NO_PRETTY_TYPES=FALSE -D NO_PRETTY_VALUES=FALSE -D TEST_LIST=sql_objects_tests_TESTS -D "CTEST_FILE=E:/Projects/Code/AI Agent/database_v13/build/tests/sql_objects_tests[1]_tests.cmake" -D TEST_DISCOVERY_TIMEOUT=5 -D TEST_XML_OUTPUT_DIR= -P "C:/Program Files/CMake/share/cmake-3.28/Modules/GoogleTestAddTests.cmake"
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PostBuildEvent>
    <Link>
      <AdditionalDependencies>..\lib\MinSizeRel\database_lib.lib;E:\Projects\Code\AI Agent\database_v13\third_party\googletest-distribution\lib\gtest.lib;E:\Projects\Code\AI Agent\database_v13\third_party\googletest-distribution\lib\gtest_main.lib;E:\Projects\Code\AI Agent\database_v13\third_party\googletest-distribution\lib\gmock.lib;E:\Projects\Code\AI Agent\database_v13\third_party\googletest-distribution\lib\gmock_main.lib;E:\Projects\Code\AI Agent\database_v13\third_party\sqlite\sqlite3.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>E:/Projects/Code/AI Agent/database_v13/build/lib/MinSizeRel/sql_objects_tests.lib</ImportLibrary>
      <ProgramDataBaseFile>E:/Projects/Code/AI Agent/database_v13/build/bin/MinSizeRel/sql_objects_tests.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>E:\Projects\Code\AI Agent\database_v13;E:\Projects\Code\AI Agent\database_v13\types;E:\Projects\Code\AI Agent\database_v13\utils;E:\Projects\Code\AI Agent\database_v13\tests\..;E:\Projects\Code\AI Agent\database_v13\tests\..\sql;E:\Projects\Code\AI Agent\database_v13\tests\..\types;E:\Projects\Code\AI Agent\database_v13\tests\..\utils;E:\Projects\Code\AI Agent\database_v13\tests\..\connection;E:\Projects\Code\AI Agent\database_v13\tests\..\driver;E:\Projects\Code\AI Agent\database_v13\tests\..\exception;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "E:/Projects/Code/AI Agent/database_v13/third_party/googletest-distribution/include" /Zc:__cplusplus</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <ConformanceMode>true</ConformanceMode>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <LanguageStandard>stdcpp20</LanguageStandard>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level4</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;CMAKE_INTDIR="RelWithDebInfo"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;CMAKE_INTDIR=\"RelWithDebInfo\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>E:\Projects\Code\AI Agent\database_v13;E:\Projects\Code\AI Agent\database_v13\types;E:\Projects\Code\AI Agent\database_v13\utils;E:\Projects\Code\AI Agent\database_v13\tests\..;E:\Projects\Code\AI Agent\database_v13\tests\..\sql;E:\Projects\Code\AI Agent\database_v13\tests\..\types;E:\Projects\Code\AI Agent\database_v13\tests\..\utils;E:\Projects\Code\AI Agent\database_v13\tests\..\connection;E:\Projects\Code\AI Agent\database_v13\tests\..\driver;E:\Projects\Code\AI Agent\database_v13\tests\..\exception;E:\Projects\Code\AI Agent\database_v13\third_party\googletest-distribution\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>E:\Projects\Code\AI Agent\database_v13;E:\Projects\Code\AI Agent\database_v13\types;E:\Projects\Code\AI Agent\database_v13\utils;E:\Projects\Code\AI Agent\database_v13\tests\..;E:\Projects\Code\AI Agent\database_v13\tests\..\sql;E:\Projects\Code\AI Agent\database_v13\tests\..\types;E:\Projects\Code\AI Agent\database_v13\tests\..\utils;E:\Projects\Code\AI Agent\database_v13\tests\..\connection;E:\Projects\Code\AI Agent\database_v13\tests\..\driver;E:\Projects\Code\AI Agent\database_v13\tests\..\exception;E:\Projects\Code\AI Agent\database_v13\third_party\googletest-distribution\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PostBuildEvent>
      <Message>	</Message>
      <Command>setlocal
"C:\Program Files\CMake\bin\cmake.exe" -E copy_if_different "E:/Projects/Code/AI Agent/database_v13/tests/../third_party/sqlite/sqlite3.dll" "E:/Projects/Code/AI Agent/database_v13/build/bin/RelWithDebInfo"
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
setlocal
"C:\Program Files\CMake\bin\cmake.exe" -D TEST_TARGET=sql_objects_tests -D "TEST_EXECUTABLE=E:/Projects/Code/AI Agent/database_v13/build/bin/RelWithDebInfo/sql_objects_tests.exe" -D TEST_EXECUTOR= -D "TEST_WORKING_DIR=E:/Projects/Code/AI Agent/database_v13/build/tests" -D TEST_EXTRA_ARGS= -D "TEST_PROPERTIES=VS_DEBUGGER_WORKING_DIRECTORY";"E:/Projects/Code/AI Agent/database_v13/build/tests" -D TEST_PREFIX= -D TEST_SUFFIX= -D TEST_FILTER= -D NO_PRETTY_TYPES=FALSE -D NO_PRETTY_VALUES=FALSE -D TEST_LIST=sql_objects_tests_TESTS -D "CTEST_FILE=E:/Projects/Code/AI Agent/database_v13/build/tests/sql_objects_tests[1]_tests.cmake" -D TEST_DISCOVERY_TIMEOUT=5 -D TEST_XML_OUTPUT_DIR= -P "C:/Program Files/CMake/share/cmake-3.28/Modules/GoogleTestAddTests.cmake"
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PostBuildEvent>
    <Link>
      <AdditionalDependencies>..\lib\RelWithDebInfo\database_lib.lib;E:\Projects\Code\AI Agent\database_v13\third_party\googletest-distribution\lib\gtest.lib;E:\Projects\Code\AI Agent\database_v13\third_party\googletest-distribution\lib\gtest_main.lib;E:\Projects\Code\AI Agent\database_v13\third_party\googletest-distribution\lib\gmock.lib;E:\Projects\Code\AI Agent\database_v13\third_party\googletest-distribution\lib\gmock_main.lib;E:\Projects\Code\AI Agent\database_v13\third_party\sqlite\sqlite3.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>E:/Projects/Code/AI Agent/database_v13/build/lib/RelWithDebInfo/sql_objects_tests.lib</ImportLibrary>
      <ProgramDataBaseFile>E:/Projects/Code/AI Agent/database_v13/build/bin/RelWithDebInfo/sql_objects_tests.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="E:\Projects\Code\AI Agent\database_v13\tests\CMakeLists.txt">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Building Custom Rule E:/Projects/Code/AI Agent/database_v13/tests/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" "-SE:/Projects/Code/AI Agent/database_v13" "-BE:/Projects/Code/AI Agent/database_v13/build" --check-stamp-file "E:/Projects/Code/AI Agent/database_v13/build/tests/CMakeFiles/generate.stamp"
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Program Files\CMake\share\cmake-3.28\Modules\CheckCXXSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\CheckIncludeFileCXX.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\CheckLibraryExists.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\FindGTest.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\FindPackageMessage.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\FindThreads.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\GoogleTest.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\Internal\CheckSourceCompiles.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">E:\Projects\Code\AI Agent\database_v13\build\tests\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Building Custom Rule E:/Projects/Code/AI Agent/database_v13/tests/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" "-SE:/Projects/Code/AI Agent/database_v13" "-BE:/Projects/Code/AI Agent/database_v13/build" --check-stamp-file "E:/Projects/Code/AI Agent/database_v13/build/tests/CMakeFiles/generate.stamp"
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Program Files\CMake\share\cmake-3.28\Modules\CheckCXXSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\CheckIncludeFileCXX.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\CheckLibraryExists.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\FindGTest.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\FindPackageMessage.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\FindThreads.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\GoogleTest.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\Internal\CheckSourceCompiles.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">E:\Projects\Code\AI Agent\database_v13\build\tests\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Building Custom Rule E:/Projects/Code/AI Agent/database_v13/tests/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" "-SE:/Projects/Code/AI Agent/database_v13" "-BE:/Projects/Code/AI Agent/database_v13/build" --check-stamp-file "E:/Projects/Code/AI Agent/database_v13/build/tests/CMakeFiles/generate.stamp"
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">C:\Program Files\CMake\share\cmake-3.28\Modules\CheckCXXSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\CheckIncludeFileCXX.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\CheckLibraryExists.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\FindGTest.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\FindPackageMessage.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\FindThreads.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\GoogleTest.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\Internal\CheckSourceCompiles.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">E:\Projects\Code\AI Agent\database_v13\build\tests\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Building Custom Rule E:/Projects/Code/AI Agent/database_v13/tests/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" "-SE:/Projects/Code/AI Agent/database_v13" "-BE:/Projects/Code/AI Agent/database_v13/build" --check-stamp-file "E:/Projects/Code/AI Agent/database_v13/build/tests/CMakeFiles/generate.stamp"
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">C:\Program Files\CMake\share\cmake-3.28\Modules\CheckCXXSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\CheckIncludeFileCXX.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\CheckLibraryExists.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\FindGTest.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\FindPackageMessage.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\FindThreads.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\GoogleTest.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\Internal\CheckSourceCompiles.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">E:\Projects\Code\AI Agent\database_v13\build\tests\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="E:\Projects\Code\AI Agent\database_v13\tests\test_sql_object.cpp" />
    <ClCompile Include="E:\Projects\Code\AI Agent\database_v13\tests\test_sql_column.cpp" />
    <ClCompile Include="E:\Projects\Code\AI Agent\database_v13\tests\test_sql_index.cpp" />
    <ClCompile Include="E:\Projects\Code\AI Agent\database_v13\tests\test_sql_row.cpp" />
    <ClCompile Include="E:\Projects\Code\AI Agent\database_v13\tests\test_sql_table.cpp" />
    <ClCompile Include="E:\Projects\Code\AI Agent\database_v13\tests\test_sql_view.cpp" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="E:\Projects\Code\AI Agent\database_v13\build\ZERO_CHECK.vcxproj">
      <Project>{742F3656-6D38-381A-A919-6A23E14D05C4}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="E:\Projects\Code\AI Agent\database_v13\build\database_lib.vcxproj">
      <Project>{04BD33EA-F433-3312-8974-760B97926F95}</Project>
      <Name>database_lib</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>