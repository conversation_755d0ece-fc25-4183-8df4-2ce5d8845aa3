#include "sql_view.h"

#include <sstream>

#include "sql_database.h"
#include "sql_query.h"
#include "sql_column.h"
#include "sql_row.h"
#include "exception/sql_error.h"

namespace database {

/**
 * @brief Private implementation class for SqlView
 *
 * This class provides the database interaction layer (Tier 3) for SqlView.
 * It manages the actual database operations and maintains connection state.
 */
class SqlViewPrivate {
public:
    SqlViewPrivate() = default;
    ~SqlViewPrivate() = default;

    // Database connection
    std::shared_ptr<SqlDatabase> database;

    // Error information
    SqlError lastError;

    // State flags
    bool databaseEnabled = false;

    void setDatabase(std::shared_ptr<SqlDatabase> db) {
        database = db;
        databaseEnabled = (db != nullptr);
    }

    bool isValid() const {
        return databaseEnabled && database && database->isOpen();
    }

    void setError(const std::string& message, ErrorCode code = ErrorCode::Unknown) {
        lastError = SqlError(message, code);
    }

    void clearError() {
        lastError.clear();
    }
};

//----------------------------------------------------------------------
// Constructors and Destructors
//----------------------------------------------------------------------

SqlView::SqlView() noexcept
    : SqlObject("", SqlObjectType::View) {
}

SqlView::SqlView(std::string_view name) noexcept
    : SqlObject(name, SqlObjectType::View) {
}

SqlView::SqlView(std::string_view name, std::string_view definition)
    : SqlObject(name, SqlObjectType::View) {
    // Initialize metadata with definition
    if (!m_metadata) {
        m_metadata = std::make_shared<SqlViewMetadata>();
    }
    m_metadata->definition = definition;
}

SqlView::SqlView(std::string_view name, SqlViewMetadata metadata)
    : SqlObject(name, SqlObjectType::View) {
    m_metadata = std::make_shared<SqlViewMetadata>(metadata);
}

SqlView::SqlView(std::string_view name, SqlViewMetadata metadata, std::shared_ptr<SqlDatabase> database)
    : SqlObject(name, SqlObjectType::View) {
    m_metadata = std::make_shared<SqlViewMetadata>(metadata);

    // Set up database connection
    if (database) {
        if (!d_ptr) {
            d_ptr = std::make_shared<SqlViewPrivate>();
        }
        d_ptr->setDatabase(database);
    }
}

SqlView::~SqlView() = default;

// Copy constructor
SqlView::SqlView(const SqlView& other)
    : SqlObject(other), m_metadata(other.m_metadata), d_ptr(other.d_ptr) {
}

// Copy assignment operator
SqlView& SqlView::operator=(const SqlView& other) {
    if (this != &other) {
        SqlObject::operator=(other);
        m_metadata = other.m_metadata;
        d_ptr = other.d_ptr;
    }
    return *this;
}

// Move constructor
SqlView::SqlView(SqlView&& other) noexcept
    : SqlObject(std::move(other)), m_metadata(std::move(other.m_metadata)),
    d_ptr(std::move(other.d_ptr)) {
}

// Move assignment operator
SqlView& SqlView::operator=(SqlView&& other) noexcept {
    if (this != &other) {
        SqlObject::operator=(std::move(other));
        m_metadata = std::move(other.m_metadata);
        d_ptr = std::move(other.d_ptr);
    }
    return *this;
}

//----------------------------------------------------------------------
// Extended Metadata Operations
//----------------------------------------------------------------------

bool SqlView::hasMetadata() const noexcept {
    return m_metadata != nullptr;
}

SqlView::SqlViewMetadata* SqlView::metadata() const noexcept {
    // Lazy initialization of metadata
    if (!m_metadata) {
        m_metadata = std::make_shared<SqlViewMetadata>();
    }
    return m_metadata.get();
}

SqlView& SqlView::setMetadata(const SqlViewMetadata& metadata) {
    m_metadata = std::make_shared<SqlViewMetadata>(std::move(metadata));
    return *this;
}

std::string_view SqlView::schema() const noexcept {
    return hasMetadata() ? std::string_view(m_metadata->schema) : std::string_view{};
}

SqlView& SqlView::setSchema(std::string_view schema) {
    metadata()->schema = schema;
    return *this;
}

std::string_view SqlView::definition() const noexcept {
     return hasMetadata() ? std::string_view(m_metadata->definition) : std::string_view{};
}

SqlView& SqlView::setDefinition(std::string_view definition) {
    metadata()->definition = definition;
    return *this;
}

std::string_view SqlView::comment() const noexcept {
    return hasMetadata() ? std::string_view(m_metadata->comment) : std::string_view{};
}

SqlView& SqlView::setComment(std::string_view comment) {
    metadata()->comment = comment;
    return *this;
}

std::vector<std::string> SqlView::referencedTables() const noexcept {
    return hasMetadata() ? m_metadata->referencedTables : std::vector<std::string>{};
}

std::vector<std::string> SqlView::columnNames() const noexcept {
    return hasMetadata() ? m_metadata->columns : std::vector<std::string>{};
}

bool SqlView::isUpdatable() const noexcept {
    return hasMetadata() ? m_metadata->isUpdatable : false;
}

SqlView& SqlView::setUpdatable(bool updatable) noexcept {
    metadata()->isUpdatable = updatable;
    return *this;
}

bool SqlView::isMaterialized() const noexcept {
    return hasMetadata() ? m_metadata->isMaterialized : false;
}

SqlView& SqlView::setMaterialized(bool materialized) noexcept {
    metadata()->isMaterialized = materialized;
    return *this;
}

bool SqlView::hasCheckOption() const noexcept {
    return hasMetadata() ? m_metadata->withCheckOption : false;
}

SqlView& SqlView::setCheckOption(bool withCheckOption) noexcept {
    metadata()->withCheckOption = withCheckOption;
    return *this;
}

std::string_view SqlView::checkOptionType() const noexcept {
    return hasMetadata() ? std::string_view(m_metadata->checkOption) : std::string_view{};
}

SqlView& SqlView::setCheckOptionType(std::string_view checkOption) {
    metadata()->checkOption = checkOption;
    metadata()->withCheckOption = !m_metadata->checkOption.empty();
    return *this;
}

std::string_view SqlView::securityType() const noexcept {
    return hasMetadata() ? std::string_view(m_metadata->securityType) : std::string_view{};
}

SqlView& SqlView::setSecurityType(std::string_view securityType) {
    metadata()->securityType = securityType;
    return *this;
}

std::string_view SqlView::definer() const noexcept {
    return hasMetadata() ? std::string_view(m_metadata->definer) : std::string_view{};
}

SqlView& SqlView::setDefiner(std::string_view definer) {
    metadata()->definer = definer;
    return *this;
}

//----------------------------------------------------------------------
// Database Interaction Operations
//----------------------------------------------------------------------

bool SqlView::isConnected() const noexcept {
    return d_ptr && d_ptr->isValid();
}

std::shared_ptr<SqlDatabase> SqlView::database() const {
    return d_ptr ? d_ptr->database : nullptr;
}

void SqlView::setDatabase(std::shared_ptr<SqlDatabase> database) {
    if (!d_ptr) {
        d_ptr = std::make_shared<SqlViewPrivate>();
    }
    d_ptr->database = std::move(database);
}

bool SqlView::exists() const {
    if (!d_ptr || !d_ptr->isValid()) {
        return false;
    }
    
    try {
        SqlQuery query(*d_ptr->database);

        // Query to check if view exists
        std::string sql = "SELECT COUNT(*) FROM INFORMATION_SCHEMA.VIEWS WHERE TABLE_NAME = ?";

        if (!schema().empty()) {
            sql += " AND TABLE_SCHEMA = ?";
        }

        query.setQuery(sql).prepare();
        query.bind(1, std::string(name()));

        if (!schema().empty()) {
            query.bind(2, schema());
        }

        if (!query.execute() || !query.next()) {
            return false;
        }

        return query.value(0).to<int>() > 0;

    } catch (const std::exception&) {
        return false;
    }
}

bool SqlView::create(bool orReplace) {
    if (!d_ptr || !d_ptr->isValid()) {
        if (d_ptr) {
            d_ptr->setError("No database connection available");
        }
        return false;
    }

    if (definition().empty()) {
        if (d_ptr) {
            d_ptr->setError("View definition is empty");
        }
        return false;
    }

    try {
        SqlQuery query(*d_ptr->database);

        // Build CREATE VIEW statement
        std::string sql = createStatement(orReplace);

        query.setQuery(sql);

        if (!query.execute()) {
            d_ptr->setError(std::format("Failed to create view: {}", query.lastError().message()));
            return false;
        }

        // Update metadata
        if (m_metadata) {
            m_metadata->createdTime = std::chrono::system_clock::now();
        }

        d_ptr->clearError();
        return true;

    } catch (const std::exception& e) {
        d_ptr->setError("Exception creating view: " + std::string(e.what()));
        return false;
    }
}

bool SqlView::drop(bool ifExists) {
    if (!d_ptr || !d_ptr->isValid()) {
        if (d_ptr) {
            d_ptr->setError("No database connection available");
        }
        return false;
    }

    try {
        SqlQuery query(*d_ptr->database);

        // Build DROP VIEW statement
        std::ostringstream sql;
        sql << "DROP VIEW ";
        if (ifExists) {
            sql << "IF EXISTS ";
        }
        sql << qualifiedName();

        query.setQuery(sql.str());

        if (!query.execute()) {
            d_ptr->setError(std::format("Failed to drop view: {}", query.lastError().message()));
            return false;
        }

        d_ptr->clearError();
        return true;

    } catch (const std::exception& e) {
        d_ptr->setError("Exception dropping view: " + std::string(e.what()));
        return false;
    }
}

bool SqlView::rename(std::string_view newName) {
    if (!d_ptr || !d_ptr->isValid()) {
        if (d_ptr) {
            d_ptr->setError("No database connection available");
        }
        return false;
    }

    try {
        SqlQuery query(*d_ptr->database);

        // Build RENAME VIEW statement (database-specific)
        std::string sql = "ALTER VIEW " + qualifiedName() + " RENAME TO " + std::string(newName);

        query.setQuery(sql);

        if (!query.execute()) {
            d_ptr->setError(std::format("Failed to rename view: {}", query.lastError().message()));
            return false;
        }

        // Update object name
        setName(newName);

        d_ptr->clearError();
        return true;

    } catch (const std::exception& e) {
        d_ptr->setError("Exception renaming view: " + std::string(e.what()));
        return false;
    }
}

bool SqlView::refresh() const {
    if (!d_ptr || !d_ptr->isValid()) {
        if (d_ptr) {
            d_ptr->setError("No database connection available");
        }
        return false;
    }

    if (!isMaterialized()) {
        if (d_ptr) {
            d_ptr->setError("View is not materialized");
        }
        return false;
    }

    try {
        SqlQuery query(*d_ptr->database);

        // Build REFRESH MATERIALIZED VIEW statement
        std::string sql = "REFRESH MATERIALIZED VIEW " + qualifiedName();

        query.setQuery(sql);

        if (!query.execute()) {
            d_ptr->setError(std::format("Failed to refresh materialized view: {}", query.lastError().message()));
            return false;
        }

        // Update metadata
        if (m_metadata) {
            m_metadata->lastRefreshTime = std::chrono::system_clock::now();
        }

        d_ptr->clearError();
        return true;

    } catch (const std::exception& e) {
        d_ptr->setError("Exception refreshing materialized view: " + std::string(e.what()));
        return false;
    }
}

//----------------------------------------------------------------------
// Data Access Methods
//----------------------------------------------------------------------

std::vector<SqlColumn> SqlView::columns() const {
    std::vector<SqlColumn> result;

    if (!d_ptr || !d_ptr->isValid()) {
        return result;
    }

    try {
        SqlQuery query(*d_ptr->database);

        // Query to get column information
        std::string sql = "SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT "
                          "FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = ?";

        if (!schema().empty()) {
            sql += " AND TABLE_SCHEMA = ?";
        }
        sql += " ORDER BY ORDINAL_POSITION";

        query.setQuery(sql).prepare();
        query.bind(1, std::string(name()));

        if (!schema().empty()) {
            query.bind(2, schema());
        }

        if (!query.execute()) {
            return result;
        }

        while (query.next()) {
            SqlColumn column(query.value("COLUMN_NAME").to<std::string>());

            // Set column metadata from query results
            auto columnMetadata = std::make_shared<SqlColumn::SqlColumnMetadata>();
            columnMetadata->tableName = std::string(name());
            columnMetadata->dataType = stringToSqlDataType(query.value("DATA_TYPE").to<std::string>());
            columnMetadata->isNullable = (query.value("IS_NULLABLE").to<std::string>() == "YES");

            auto defaultValue = query.value("COLUMN_DEFAULT");
            if (!defaultValue.isNull()) {
                columnMetadata->defaultValue = defaultValue;
            }

            column.setMetadata(*columnMetadata);
            result.push_back(std::move(column));
        }

    } catch (const std::exception&) {
        // Return empty vector on error
    }

    return result;
}

SqlColumn SqlView::column(const std::string& columnName) const {
    auto allColumns = columns();

    for (const auto& col : allColumns) {
        if (col.name() == columnName) {
            return col;
        }
    }

    // Return invalid column if not found
    return SqlColumn{};
}

size_t SqlView::rowCount() const {
    if (!d_ptr || !d_ptr->isValid()) {
        return 0;
    }

    try {
        SqlQuery query(*d_ptr->database);

        // Build COUNT query
        std::string sql = "SELECT COUNT(*) FROM " + qualifiedName();

        query.setQuery(sql);

        if (!query.execute() || !query.next()) {
            return 0;
        }

        return query.value(0).to<size_t>();

    } catch (const std::exception&) {
        return 0;
    }
}

std::vector<SqlRow> SqlView::selectAll() const {
    return select("", {});
}

std::vector<SqlRow> SqlView::select(const std::string& whereClause,
                                    const std::vector<Data>& parameters) const {
    std::vector<SqlRow> result;

    if (!d_ptr || !d_ptr->isValid()) {
        return result;
    }

    try {
        SqlQuery query(*d_ptr->database);

        // Build SELECT statement
        std::string sql = "SELECT * FROM " + qualifiedName();

        if (!whereClause.empty()) {
            sql += " WHERE " + whereClause;
        }

        query.setQuery(sql).prepare();

        // Bind parameters
        for (size_t i = 0; i < parameters.size(); ++i) {
            query.bind(static_cast<int>(i + 1), parameters[i]);
        }

        if (!query.execute()) {
            return result;
        }

        while (query.next()) {
            SqlRow row(name());

            auto record = query.record();
            auto fieldNames = record.fieldNames();

            for (const auto& fieldName : fieldNames) {
                row.setValue(fieldName, record.value(fieldName));
            }

            result.push_back(std::move(row));
        }

    } catch (const std::exception&) {
        // Return empty vector on error
    }

    return result;
}

std::vector<SqlRow> SqlView::select(size_t limit, size_t offset) const {
    std::vector<SqlRow> result;

    if (!d_ptr || !d_ptr->isValid()) {
        return result;
    }

    try {
        SqlQuery query(*d_ptr->database);

        // Build SELECT statement with LIMIT and OFFSET
        std::string sql = "SELECT * FROM " + qualifiedName() +
                          " LIMIT " + std::to_string(limit) +
                          " OFFSET " + std::to_string(offset);

        query.setQuery(sql);

        if (!query.execute()) {
            return result;
        }

        while (query.next()) {
            SqlRow row(name());

            auto record = query.record();
            auto fieldNames = record.fieldNames();

            for (const auto& fieldName : fieldNames) {
                row.setValue(fieldName, record.value(fieldName));
            }

            result.push_back(std::move(row));
        }

    } catch (const std::exception&) {
        // Return empty vector on error
    }

    return result;
}

//----------------------------------------------------------------------
// Update/Delete Operations (for updatable views)
//----------------------------------------------------------------------

bool SqlView::insert(const SqlRow& row) {
    if (!d_ptr || !d_ptr->isValid()) {
        if (d_ptr) {
            d_ptr->setError("No database connection available");
        }
        return false;
    }

    if (!isUpdatable()) {
        if (d_ptr) {
            d_ptr->setError("View is not updatable");
        }
        return false;
    }

    try {
        SqlQuery query(*d_ptr->database);

        // Build INSERT statement
        std::string sql = row.toInsertStatement(qualifiedName());

        query.setQuery(sql).prepare();

        // Bind values
        size_t paramIndex = 1;
        for (const auto& pair : row.values()) {
            query.bind(static_cast<int>(paramIndex++), pair.second);
        }

        if (!query.execute()) {
            d_ptr->setError(std::format("Failed to insert into view: {}", query.lastError().message()));
            return false;
        }

        d_ptr->clearError();
        return true;

    } catch (const std::exception& e) {
        d_ptr->setError("Exception inserting into view: " + std::string(e.what()));
        return false;
    }
}

size_t SqlView::update(const SqlRow& row, const std::string& whereClause,
                       const std::vector<Data>& parameters) {
    if (!d_ptr || !d_ptr->isValid()) {
        return 0;
    }

    if (!isUpdatable()) {
        return 0;
    }

    try {
        SqlQuery query(*d_ptr->database);

        // Build UPDATE statement
        std::string sql = row.toUpdateStatement(whereClause, qualifiedName());

        query.setQuery(sql).prepare();

        // Bind field values
        size_t paramIndex = 1;
        for (const auto& pair : row.values()) {
            query.bind(static_cast<int>(paramIndex++), pair.second);
        }

        // Bind WHERE clause parameters
        for (const auto& param : parameters) {
            query.bind(static_cast<int>(paramIndex++), param);
        }

        if (!query.execute()) {
            return 0;
        }

        return query.numRowsAffected();

    } catch (const std::exception&) {
        return 0;
    }
}

size_t SqlView::deleteRows(const std::string& whereClause,
                           const std::vector<Data>& parameters) {
    if (!d_ptr || !d_ptr->isValid()) {
        return 0;
    }

    if (!isUpdatable()) {
        return 0;
    }

    try {
        SqlQuery query(*d_ptr->database);

        // Build DELETE statement
        std::string sql = "DELETE FROM " + qualifiedName();

        if (!whereClause.empty()) {
            sql += " WHERE " + whereClause;
        }

        query.setQuery(sql).prepare();

        // Bind parameters
        for (size_t i = 0; i < parameters.size(); ++i) {
            query.bind(static_cast<int>(i + 1), parameters[i]);
        }

        if (!query.execute()) {
            return 0;
        }

        return query.numRowsAffected();

    } catch (const std::exception&) {
        return 0;
    }
}

//----------------------------------------------------------------------
// Analysis and Utility Methods
//----------------------------------------------------------------------

std::vector<std::string> SqlView::dependencies() const {
    std::vector<std::string> result;

    if (!d_ptr || !d_ptr->isValid()) {
        return result;
    }

    try {
        SqlQuery query(*d_ptr->database);

        // Query to get view dependencies (database-specific)
        std::string sql = "SELECT REFERENCED_TABLE_NAME FROM INFORMATION_SCHEMA.VIEW_TABLE_USAGE WHERE VIEW_NAME = ?";

        if (!schema().empty()) {
            sql += " AND VIEW_SCHEMA = ?";
        }

        query.setQuery(sql).prepare();
        query.bind(1, std::string(name()));

        if (!schema().empty()) {
            query.bind(2, schema());
        }

        if (!query.execute()) {
            return result;
        }

        while (query.next()) {
            std::string tableName = query.value("REFERENCED_TABLE_NAME").to<std::string>();
            if (!tableName.empty()) {
                result.push_back(tableName);
            }
        }

    } catch (const std::exception&) {
        // Return empty vector on error
    }

    return result;
}

bool SqlView::isDefinitionValid() const {
    if (definition().empty()) {
        return false;
    }

    if (!d_ptr || !d_ptr->isValid()) {
        return true; // Can't validate without database connection
    }

    try {
        SqlQuery query(*d_ptr->database);

        // Try to execute EXPLAIN on the view definition
        std::string sql = "EXPLAIN " + std::string(definition());

        query.setQuery(sql);

        return query.execute();

    } catch (const std::exception&) {
        return false;
    }
}

bool SqlView::refreshMetadata() {
    if (!d_ptr || !d_ptr->isValid()) {
        return false;
    }

    try {
        SqlQuery query(*d_ptr->database);

        // Query to get view metadata
        std::string sql = "SELECT VIEW_DEFINITION, IS_UPDATABLE, CHECK_OPTION, DEFINER, SECURITY_TYPE "
                          "FROM INFORMATION_SCHEMA.VIEWS WHERE TABLE_NAME = ?";

        if (!schema().empty()) {
            sql += " AND TABLE_SCHEMA = ?";
        }

        query.setQuery(sql).prepare();
        query.bind(1, std::string(name()));

        if (!schema().empty()) {
            query.bind(2, schema());
        }

        if (!query.execute() || !query.next()) {
            return false;
        }

        // Update metadata from query results
        if (!m_metadata) {
            m_metadata = std::make_shared<SqlViewMetadata>();
        }

        m_metadata->definition = query.value("VIEW_DEFINITION").to<std::string>();
        m_metadata->isUpdatable = (query.value("IS_UPDATABLE").to<std::string>() == "YES");
        m_metadata->checkOption = query.value("CHECK_OPTION").to<std::string>();
        m_metadata->withCheckOption = !m_metadata->checkOption.empty();
        m_metadata->definer = query.value("DEFINER").to<std::string>();
        m_metadata->securityType = query.value("SECURITY_TYPE").to<std::string>();

        return true;

    } catch (const std::exception&) {
        return false;
    }
}

bool SqlView::execute(const std::string& sql, const std::vector<Data>& parameters) {
    if (!d_ptr || !d_ptr->isValid()) {
        return false;
    }

    try {
        SqlQuery query(*d_ptr->database);

        query.setQuery(sql).prepare();

        // Bind parameters
        for (size_t i = 0; i < parameters.size(); ++i) {
            query.bind(static_cast<int>(i + 1), parameters[i]);
        }

        return query.execute();

    } catch (const std::exception&) {
        return false;
    }
}

//----------------------------------------------------------------------
// SQL Generation Methods
//----------------------------------------------------------------------

std::string SqlView::qualifiedName() const {
    if (hasMetadata() && !m_metadata->schema.empty()) {
        return m_metadata->schema + "." + std::string(name());
    }
    return std::string(name());
}

std::string SqlView::toSql() const {
    return createStatement();
}

std::string SqlView::createStatement(bool orReplace) const {
    if (!hasMetadata() || m_metadata->definition.empty()) {
        return "";
    }
    
    std::ostringstream sql;

    if (orReplace) {
        sql << "CREATE OR REPLACE ";
    } else {
        sql << "CREATE ";
    }

    // Start with CREATE statement
    if (m_metadata->isMaterialized) {
        sql << "MATERIALIZED VIEW ";
    } else {
        sql << "VIEW ";
    }
    
    sql << qualifiedName();
    
    // Add column list if specified
    if (!m_metadata->columns.empty()) {
        sql << " (";
        for (size_t i = 0; i < m_metadata->columns.size(); ++i) {
            if (i > 0) sql << ", ";
            sql << m_metadata->columns[i];
        }
        sql << ")";
    }
    
    sql << " AS " << m_metadata->definition;
    
    // Add WITH CHECK OPTION if specified
    if (m_metadata->withCheckOption) {
        sql << " WITH ";
        if (!m_metadata->checkOption.empty()) {
            sql << m_metadata->checkOption << " ";
        }
        sql << "CHECK OPTION";
    }
    
    return sql.str();
}

std::string SqlView::dropStatement() const {
    return "DROP VIEW " + qualifiedName();
}

//----------------------------------------------------------------------
// Static Factory Methods
//----------------------------------------------------------------------

SqlView SqlView::fromDatabase(std::shared_ptr<SqlDatabase> database,
                              const std::string& viewName,
                              const std::string& schema,
                              bool loadMetadata) {
    SqlView view(viewName);

    if (!schema.empty()) {
        view.setSchema(schema);
    }

    if (database) {
        view.setDatabase(database);

        if (loadMetadata) {
            view.refreshMetadata();
        }
    }

    return view;
}

//----------------------------------------------------------------------
// Helper Methods
//----------------------------------------------------------------------

/*SqlError SqlView::lastError() const {
    if (d_ptr) {
        return d_ptr->lastError;
    }
    return SqlError{};
}

void SqlView::clearError() {
    if (d_ptr) {
        d_ptr->clearError();
    }
}*/

} // namespace database 
