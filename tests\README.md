# SQL Object Hierarchy Unit Tests

This directory contains comprehensive unit tests for the SQL object hierarchy in the database library. The tests are built using Google Test framework and provide thorough coverage of all SQL object classes.

## Test Structure

### Test Files

- **`test_sql_object.cpp`** - Tests for the base `SqlObject` class
- **`test_sql_column.cpp`** - Tests for the `SqlColumn` class
- **`test_sql_index.cpp`** - Tests for the `SqlIndex` class
- **`test_sql_row.cpp`** - Tests for the `SqlRow` class
- **`test_sql_table.cpp`** - Tests for the `SqlTable` class
- **`test_sql_view.cpp`** - Tests for the `SqlView` class
- **`test_utils.h`** - Common testing utilities and helpers

### Test Categories

Each test file covers the following categories:

1. **Constructor Tests** - Testing various constructor overloads
2. **Property Management** - Testing getters and setters
3. **Data Validation** - Testing data type validation and constraints
4. **SQL Generation** - Testing SQL statement generation
5. **Copy/Move Semantics** - Testing copy and move operations
6. **Equality/Comparison** - Testing comparison operators
7. **Edge Cases** - Testing boundary conditions and error cases
8. **Parameterized Tests** - Testing with various data sets

## Test Coverage

### SqlObject Base Class Tests

- Default, name, and name+type constructors
- Name getter/setter functionality
- Object type management
- Validity checks
- Equality and comparison operators
- Hash function
- Copy and move semantics
- Polymorphic behavior with concrete implementations

### SqlColumn Tests

- Constructor variations (default, name, name+datatype)
- Data type management (all SQL data types)
- Length, precision, and scale management
- Constraint management (NOT NULL, UNIQUE, PRIMARY KEY, etc.)
- Default value handling
- Table name association
- Comment management
- SQL generation for column definitions
- Qualified name generation
- Parameterized tests for all data types and constraints

### SqlIndex Tests

- Constructor variations (default, name, name+type, name+table)
- Index type management (PRIMARY, UNIQUE, NORMAL, HASH, etc.)
- Column management (add, remove, clear columns)
- Sort order management (ASC, DESC)
- Table name association
- Comment management
- SQL generation for index creation/dropping
- Factory methods (createPrimaryKey, createUnique, createIndex)
- Hash functionality for unordered containers
- Parameterized tests for all index types

### SqlRow Tests

- Constructor variations
- Value management (set, get, remove values)
- Column management (names, existence checks)
- Table name association
- SQL generation (INSERT, UPDATE, DELETE statements)
- JSON and XML serialization
- Different data types handling
- Edge cases (null values, empty strings, large values)

### SqlTable Tests

- Constructor variations (default, name, name+schema, name+columns)
- Metadata management (schema, engine, comment, etc.)
- Column management (add, get, count columns)
- Index management (add, get, count indexes)
- Schema and alias management
- SQL generation (CREATE, DROP, TRUNCATE statements)
- Primary key and foreign key detection
- Complex schema handling
- Edge cases (empty names, special characters)

### SqlView Tests

- Constructor variations (default, name, name+query)
- Query management
- Metadata management (schema, comment, updatable flag)
- Dependency management (add, remove, clear dependencies)
- Schema management
- SQL generation (CREATE VIEW, DROP VIEW statements)
- Complex query handling (JOINs, aggregates, subqueries)
- Edge cases (multiline queries, empty queries)

## Test Utilities

The `test_utils.h` file provides:

### Base Test Fixture
- `SqlObjectTestBase` - Common setup/teardown for all tests

### Test Data Generators
- `TestDataGenerator::randomString()` - Generate random strings
- `TestDataGenerator::randomInt()` - Generate random integers
- `TestDataGenerator::createTestColumn()` - Create test columns
- `TestDataGenerator::createTestRow()` - Create test rows
- `TestDataGenerator::createTestTable()` - Create test tables
- `TestDataGenerator::createTestIndex()` - Create test indexes
- `TestDataGenerator::createTestView()` - Create test views

### Custom Matchers
- `HasName(expectedName)` - Check object name
- `HasObjectType(expectedType)` - Check object type
- `IsValid()` / `IsNotValid()` - Check object validity

### Helper Macros
- `EXPECT_SQL_OBJECT_VALID(obj)` - Assert object is valid
- `EXPECT_SQL_OBJECT_INVALID(obj)` - Assert object is invalid
- `EXPECT_SQL_OBJECT_NAME(obj, name)` - Assert object name
- `EXPECT_SQL_OBJECT_TYPE(obj, type)` - Assert object type

### Test Data Sets
- `TestDataSets::getAllDataTypes()` - All SQL data types for parameterized tests
- `TestDataSets::getAllConstraints()` - All column constraints for parameterized tests
- `TestDataSets::getAllIndexTypes()` - All index types for parameterized tests

## Running Tests

### Build and Run All Tests
```bash
mkdir build
cd build
cmake ..
make
./tests/sql_objects_tests
```

### Run Specific Test Suites
```bash
# Run only SqlObject tests
./tests/sql_objects_tests --gtest_filter="SqlObjectTest.*"

# Run only SqlColumn tests
./tests/sql_objects_tests --gtest_filter="SqlColumnTest.*"

# Run only SqlIndex tests
./tests/sql_objects_tests --gtest_filter="SqlIndexTest.*"

# Run only SqlRow tests
./tests/sql_objects_tests --gtest_filter="SqlRowTest.*"

# Run only SqlTable tests
./tests/sql_objects_tests --gtest_filter="SqlTableTest.*"

# Run only SqlView tests
./tests/sql_objects_tests --gtest_filter="SqlViewTest.*"
```

### Run with Verbose Output
```bash
./tests/sql_objects_tests --gtest_verbose
```

### Generate Test Report
```bash
./tests/sql_objects_tests --gtest_output=xml:test_results.xml
```

## Test Philosophy

These tests follow several key principles:

1. **Comprehensive Coverage** - Every public method and property is tested
2. **Edge Case Testing** - Boundary conditions and error cases are thoroughly tested
3. **Parameterized Testing** - Common patterns are tested with multiple data sets
4. **Isolation** - Each test is independent and can run in any order
5. **Readability** - Tests are well-documented and easy to understand
6. **Maintainability** - Common functionality is extracted into utilities

## Adding New Tests

When adding new functionality to SQL objects:

1. Add corresponding tests to the appropriate test file
2. Use the existing test patterns and utilities
3. Include both positive and negative test cases
4. Add parameterized tests for data-driven scenarios
5. Update this README if new test categories are added

## Dependencies

- Google Test (gtest) - Testing framework
- Google Mock (gmock) - Mocking framework (for future database mocking)
- C++20 standard library
- Database library SQL objects
