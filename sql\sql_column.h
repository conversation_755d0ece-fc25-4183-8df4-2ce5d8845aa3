#ifndef DATABASE_SQL_COLUMN_H
#define DATABASE_SQL_COLUMN_H

#include <string>
#include <string_view>
#include <memory>
#include <vector>
#include <optional>
#include <algorithm>

#include "sql_condition.h"
#include "sql_object.h"
#include "sql_types.h"

namespace database {

// Forward declarations
class SqlColumnPrivate;
class SqlTable;
class SqlDatabase;
class SqlCondition;

/**
 * @brief Class representing a database column
 *
 * This class serves as both a database column definer and accessor:
 * - Definer Mode: Define column properties for table creation/modification
 * - Accessor Mode: Access existing column metadata and generate conditions
 *
 * The class implements synchronization between local column definition
 * and actual database column metadata when connected to a database.
 */
class SqlColumn final : public SqlObject {
public:
    /**
     * @brief Simple column metadata structure
     */
    struct SqlColumnMetadata {
        SqlDataType dataType = SqlDataType::Unknown;                 ///< Column data type
        std::string tableName;                                       ///< Parent table name
        std::string comment;                                         ///< Column comment/description
        size_t maxLength = 0;                                        ///< Maximum length for string types
        std::optional<uint8_t> precision;                            ///< Precision for numeric types
        std::optional<uint8_t> scale;                                ///< Scale for decimal types
        SqlColumnConstraint constraints = SqlColumnConstraint::None; ///< Column constraints
        Data defaultValue;                            ///< Default value
        std::string checkConstraint;                                 ///< Check constraint expression
        std::string referencedTable;                                 ///< Referenced table for foreign keys
        std::string referencedColumn;                                ///< Referenced column for foreign keys
        std::string onUpdateAction;                                  ///< ON UPDATE action for foreign keys
        std::string onDeleteAction;                                  ///< ON DELETE action for foreign keys
        std::string charset;                                         ///< Character set for string types
        std::string collation;                                       ///< Collation for string types
        int ordinalPosition = 0;                                     ///< Position in table (1-based)
        bool isNullable = true;                                      ///< Can contain NULL values
        bool isAutoIncrement = false;                                ///< Is auto increment
        bool isGenerated = false;                                    ///< Is generated column
        std::string generationExpression;                            ///< Generation expression
        std::unordered_map<std::string, std::string> properties;     ///< Additional properties

        SqlColumnMetadata() = default;

        template<typename TableName, typename Comment = std::string>
        SqlColumnMetadata(TableName&& tableName, SqlDataType dataType, Comment&& comment = {})
            : dataType(dataType)
            , tableName(std::forward<TableName>(tableName))
            , comment(std::forward<Comment>(comment)) {}

        // Utility methods for constraint checking
        [[nodiscard]] bool hasConstraint(SqlColumnConstraint constraint) const noexcept {
            return (constraints & constraint) != SqlColumnConstraint::None;
        }

        void addConstraint(SqlColumnConstraint constraint) noexcept {
            constraints = constraints | constraint;
        }

        void removeConstraint(SqlColumnConstraint constraint) noexcept {
            constraints = constraints & ~constraint;
        }

        // Type checking utilities
        [[nodiscard]] bool isNumericType() const noexcept {
            return dataType == SqlDataType::Integer || dataType == SqlDataType::BigInt ||
                   dataType == SqlDataType::SmallInt || dataType == SqlDataType::TinyInt ||
                   dataType == SqlDataType::Real || dataType == SqlDataType::Float ||
                   dataType == SqlDataType::Double || dataType == SqlDataType::Decimal ||
                   dataType == SqlDataType::Numeric;
        }

        [[nodiscard]] bool isStringType() const noexcept {
            return dataType == SqlDataType::Varchar || dataType == SqlDataType::Char ||
                   dataType == SqlDataType::Text;
        }

        [[nodiscard]] bool isDateTimeType() const noexcept {
            return dataType == SqlDataType::Date || dataType == SqlDataType::Time ||
                   dataType == SqlDataType::DateTime || dataType == SqlDataType::Timestamp;
        }
    };

    SqlColumn() noexcept;
    explicit SqlColumn(std::string_view name, SqlDataType type = SqlDataType::Unknown) noexcept;
    SqlColumn(std::string_view name, const SqlTable& table) noexcept;

    // Template constructor for type-safe column creation
    template<typename T, typename = std::enable_if_t<std::is_convertible_v<T, Data>>>
    SqlColumn(std::string_view name, const SqlTable& table, T&& defaultValue)
        : SqlColumn(name, table) {
        if (auto meta = metadata()) {
            meta->defaultValue = Data(std::forward<T>(defaultValue));
        }
    }

    // Copy/move operations
    SqlColumn(const SqlColumn& other) = default;
    SqlColumn& operator=(const SqlColumn& other) = default;
    SqlColumn(SqlColumn&& other) noexcept = default;
    SqlColumn& operator=(SqlColumn&& other) noexcept = default;

    //----------------------------------------------------------------------
    // Static Methods
    //----------------------------------------------------------------------
    [[nodiscard]] static SqlColumn fromDatabase(std::string_view name, SqlDatabase* db);
    [[nodiscard]] static SqlColumn fromField(const class SqlField& field);

    //----------------------------------------------------------------------
    // Table Metadata
    //----------------------------------------------------------------------
    [[nodiscard]] SqlColumnMetadata* metadata() const noexcept;
    SqlColumn& setMetadata(const SqlColumnMetadata& metadata);
    bool loadMetadata();
    bool refreshMetadata();

    [[nodiscard]] SqlDataType dataType() const noexcept;
    SqlColumn& setDataType(SqlDataType type) noexcept;

    [[nodiscard]] std::string_view tableName() const noexcept;
    SqlColumn& setTableName(std::string tableName);

    [[nodiscard]] std::string_view comment() const noexcept;
    SqlColumn& setComment(std::string_view comment);

    [[nodiscard]] size_t maxLength() const noexcept;
    SqlColumn& setMaxLength(size_t length) noexcept;

    [[nodiscard]] std::optional<uint8_t> precision() const noexcept;
    SqlColumn& setPrecision(uint8_t precision) noexcept;

    [[nodiscard]] std::optional<uint8_t> scale() const noexcept;
    SqlColumn& setScale(uint8_t scale) noexcept;

    [[nodiscard]] SqlColumnConstraint constraints() const noexcept;
    SqlColumn& setConstraints(SqlColumnConstraint constraints) noexcept;

    SqlColumn& addConstraint(SqlColumnConstraint constraint) noexcept;
    SqlColumn& removeConstraint(SqlColumnConstraint constraint) noexcept;
    [[nodiscard]] bool hasConstraint(SqlColumnConstraint constraint) const noexcept;

    [[nodiscard]] std::optional<Data> defaultValue() const noexcept;
    SqlColumn& setDefaultValue(const Data& value);

    template<typename T>
    SqlColumn& setDefaultValue(T&& value) {
        static_assert(std::is_convertible_v<T, Data>, "Value must be convertible to Data");
        return setDefaultValue(Data(std::forward<T>(value)));
    }

    [[nodiscard]] bool isNullable() const noexcept;
    SqlColumn& setNullable(bool nullable = true) noexcept;
    SqlColumn& notNull() noexcept { return setNullable(false); }

    [[nodiscard]] bool isAutoIncrement() const noexcept;
    SqlColumn& setAutoIncrement(bool autoIncrement = true) noexcept;
    SqlColumn& autoIncrement() noexcept { return setAutoIncrement(true); }

    [[nodiscard]] bool isPrimaryKey() const noexcept;
    SqlColumn& setPrimaryKey(bool primaryKey = true) noexcept;
    SqlColumn& primaryKey() noexcept { return setPrimaryKey(true); }

    [[nodiscard]] bool isUnique() const noexcept;
    SqlColumn& setUnique(bool unique = true) noexcept;
    SqlColumn& unique() noexcept { return setUnique(true); }

    [[nodiscard]] int ordinalPosition() const noexcept;
    SqlColumn& setOrdinalPosition(int position) noexcept;

    // [[nodiscard]] const std::string& nativeTypeName() const;
    // SqlColumn& foreignKey(std::string_view referencedTable, std::string_view referencedColumn);
    // SqlColumn& check(std::string_view condition);

    //----------------------------------------------------------------------
    // Table Association
    //----------------------------------------------------------------------
    [[nodiscard]] SqlTable table() const noexcept;
    SqlColumn& setTable(const SqlTable& table) noexcept;

    [[nodiscard]] std::shared_ptr<SqlDatabase> database() const override;
    SqlColumn& setDatabase(std::shared_ptr<SqlDatabase> db);

    [[nodiscard]] bool exists() const;
    bool create(std::optional<int> position = std::nullopt);
    bool drop();
    bool modify();

    [[nodiscard]] std::vector<Data> distinctValues(std::optional<size_t> limit = std::nullopt) const;
    [[nodiscard]] std::optional<Data> minValue() const;
    [[nodiscard]] std::optional<Data> maxValue() const;
    [[nodiscard]] size_t nonNullCount() const;
    [[nodiscard]] size_t nullCount() const;
    [[nodiscard]] size_t count() const { return nonNullCount() + nullCount(); }

    [[nodiscard]] std::string qualifiedName() const override;
    [[nodiscard]] std::string toSql() const override;

    //----------------------------------------------------------------------
    // SqlObject Implementation
    //----------------------------------------------------------------------
    [[nodiscard]] std::string definitionSql() const;
    [[nodiscard]] std::string createSql() const;
    [[nodiscard]] std::string dropSql() const;
    [[nodiscard]] std::string alterSql() const;
    [[nodiscard]] std::string constraintSql() const;

    // Enhanced condition generation with fluent interface
    [[nodiscard]] SqlCondition operator==(const Variant& value) const;
    [[nodiscard]] SqlCondition operator!=(const Variant& value) const;
    [[nodiscard]] SqlCondition operator<(const Variant& value) const;
    [[nodiscard]] SqlCondition operator<=(const Variant& value) const;
    [[nodiscard]] SqlCondition operator>(const Variant& value) const;
    [[nodiscard]] SqlCondition operator>=(const Variant& value) const;

    // Modern condition methods with enhanced type safety
    template<typename T>
    [[nodiscard]] SqlCondition equals(T&& value) const {
        return eq(Variant(std::forward<T>(value)));
    }

    template<typename T>
    [[nodiscard]] SqlCondition notEquals(T&& value) const {
        return neq(Variant(std::forward<T>(value)));
    }

    template<typename Container>
    [[nodiscard]] SqlCondition in(const Container& values) const {
        std::vector<Variant> variants;
        variants.reserve(values.size());
        std::transform(values.begin(), values.end(), std::back_inserter(variants),
                       [](const auto& val) { return Variant(val); });
        return inImpl(variants);
    }

    [[nodiscard]] SqlCondition like(std::string_view pattern) const;
    [[nodiscard]] SqlCondition notLike(std::string_view pattern) const;
    [[nodiscard]] SqlCondition between(const Variant& min, const Variant& max) const;
    [[nodiscard]] SqlCondition isNull() const;
    [[nodiscard]] SqlCondition isNotNull() const;

private:
    // SqlColumn& as(std::string_view alias) noexcept;
    [[nodiscard]] SqlCondition eq(const Variant& value) const;
    [[nodiscard]] SqlCondition neq(const Variant& value) const;
    [[nodiscard]] SqlCondition lt(const Variant& value) const;
    [[nodiscard]] SqlCondition lte(const Variant& value) const;
    [[nodiscard]] SqlCondition gt(const Variant& value) const;
    [[nodiscard]] SqlCondition gte(const Variant& value) const;
    [[nodiscard]] SqlCondition inImpl(const std::vector<Variant>& values) const;
    [[nodiscard]] SqlCondition notIn(const std::vector<Variant>& values) const;

    [[nodiscard]] SqlCondition condition(std::string_view op, const Variant& value) const;
    [[nodiscard]] SqlCondition condition(std::string_view op) const;
    [[nodiscard]] SqlCondition condition(std::string_view op, const std::vector<Variant>& values) const;

private:
    mutable std::shared_ptr<SqlColumnMetadata> m_metadata { nullptr };
    mutable std::shared_ptr<SqlColumnPrivate> d_ptr { nullptr };
};

} // namespace database

// Hash function for SqlColumn
namespace std {
template<>
struct hash<database::SqlColumn> {
    [[nodiscard]] size_t operator()(const database::SqlColumn& column) const noexcept {
        // Use the base SqlObject hash combined with table information
        size_t h1 = column.hash();

        // Hash the table name if available
        size_t h2 = 0;
        try {
            auto tableName = column.tableName();
            if (!tableName.empty()) {
                h2 = std::hash<std::string_view>{}(tableName);
            }
        } catch (...) {
            // Ignore errors during hash calculation
        }

        // Hash the data type for additional uniqueness
        size_t h3 = static_cast<size_t>(column.dataType());

        return h1 ^ (h2 << 1) ^ (h3 << 2);
    }
};
} // namespace std

#endif // DATABASE_SQL_COLUMN_H
