#include "sql_index.h"

#include <sstream>
#include <algorithm>

#include "sql_query.h"
#include "sql_table.h"
#include "driver/sql_driver.h"

namespace database {

/**
 * @brief Private implementation class for SqlIndex
 *
 * This class provides the database interaction layer (Tier 3) for SqlIndex.
 * It manages the actual database operations and maintains connection state.
 */
class SqlIndexPrivate
{
public:
    SqlIndexPrivate() = default;
    ~SqlIndexPrivate() = default;

    // Database connection
    std::shared_ptr<SqlDatabase> database;

    // Associated table
    SqlTable table;

    // Error information
    SqlError lastError;

    // State flags
    bool databaseEnabled = false;

    void setDatabase(std::shared_ptr<SqlDatabase> db) {
        database = db;
        databaseEnabled = (db != nullptr);
    }

    bool isValid() const {
        return databaseEnabled && database && database->isOpen();
    }

    void setError(const std::string& message, ErrorCode code = ErrorCode::Unknown) {
        lastError = SqlError(message, code);
    }

    void clearError() {
        lastError.clear();
    }
};

//----------------------------------------------------------------------
// Constructors and Destructors
//----------------------------------------------------------------------

SqlIndex::SqlIndex() noexcept
    : SqlObject("", SqlObjectType::Index) {
}

SqlIndex::SqlIndex(std::string_view name, SqlIndexType indexType) noexcept
    : SqlObject(name, SqlObjectType::Index) {
    // Initialize metadata with basic information
    if (!m_metadata) {
        m_metadata = std::make_shared<SqlIndexMetadata>();
    }
    m_metadata->indexType = indexType;
}

SqlIndex::SqlIndex(std::string_view name, const SqlTable& table) noexcept
    : SqlObject(name, SqlObjectType::Index) {
    // Initialize metadata with table information
    if (!m_metadata) {
        m_metadata = std::make_shared<SqlIndexMetadata>();
    }
    m_metadata->tableName = table.name();

    // Set up database connection if table has one
    auto db = table.database();
    if (db) {
        if (!d_ptr) {
            d_ptr = std::make_shared<SqlIndexPrivate>();
        }
        d_ptr->setDatabase(db);
        d_ptr->table = table;
    }
}

//----------------------------------------------------------------------
// Static Factory Methods
//----------------------------------------------------------------------

SqlIndex SqlIndex::fromDatabase(std::shared_ptr<SqlTable> table,
                                const std::string& indexName,
                                bool loadMetadata) {
    SqlIndex index(indexName);

    if (!table) {
        return index;
    }

    // Initialize metadata with table information
    if (!index.m_metadata) {
        index.m_metadata = std::make_shared<SqlIndexMetadata>();
    }
    index.m_metadata->tableName = table->name();

    // Set up database connection
    auto db = table->database();
    if (db) {
        if (!index.d_ptr) {
            index.d_ptr = std::make_shared<SqlIndexPrivate>();
        }
        index.d_ptr->setDatabase(db);
        index.d_ptr->table = *table;

        if (loadMetadata) {
            index.loadMetadata();
        }
    }

    return index;
}

//----------------------------------------------------------------------
// Table Metadata
//----------------------------------------------------------------------

SqlIndex::SqlIndexMetadata* SqlIndex::metadata() const noexcept {
    // Lazy initialization of metadata
    if (!m_metadata) {
        m_metadata = std::make_shared<SqlIndexMetadata>();
    }
    return m_metadata.get();
}

SqlIndex& SqlIndex::setMetadata(const SqlIndexMetadata& metadata) {
    if (!m_metadata) {
        m_metadata = std::make_shared<SqlIndexMetadata>();
    }
    *m_metadata = metadata;
    return *this;
}

bool SqlIndex::loadMetadata() {
    if (!d_ptr || !d_ptr->isValid()) {
        return false;
    }

    try {
        SqlQuery query(*d_ptr->database);

        // Build query to get index information
        std::string sql = "SELECT * FROM INFORMATION_SCHEMA.STATISTICS WHERE INDEX_NAME = ? AND TABLE_NAME = ?";

        query.setQuery(sql).prepare();
        query.bind(1, std::string(name()))
            .bind(2, m_metadata ? m_metadata->tableName : "");

        if (!query.execute()) {
            d_ptr->setError(std::format("Failed to load index metadata: {}", query.lastError().message()));
            return false;
        }

        if (query.next()) {
            // Initialize metadata if not exists
            if (!m_metadata) {
                m_metadata = std::make_shared<SqlIndexMetadata>();
            }

            // Extract metadata from query result
            m_metadata->tableName = query.value("TABLE_NAME").to<std::string>();

            // Parse index type from database-specific information
            auto indexType = query.value("INDEX_TYPE").to<std::string>();
            if (indexType == "UNIQUE") {
                m_metadata->indexType = SqlIndexType::Unique;
                m_metadata->isUnique = true;
            } else if (indexType == "PRIMARY") {
                m_metadata->indexType = SqlIndexType::Primary;
                m_metadata->isUnique = true;
            } else {
                m_metadata->indexType = SqlIndexType::Normal;
            }

            // Get column information
            std::string columnName = query.value("COLUMN_NAME").to<std::string>();
            if (!columnName.empty()) {
                m_metadata->columns.push_back(columnName);

                // Parse sort order
                auto collation = query.value("COLLATION").to<std::string>();
                if (collation == "D") {
                    m_metadata->sortOrders.push_back(SqlSortOrder::Descending);
                } else {
                    m_metadata->sortOrders.push_back(SqlSortOrder::Ascending);
                }
            }

            d_ptr->clearError();
            return true;
        }

        d_ptr->setError("Index not found in database");
        return false;

    } catch (const std::exception& e) {
        d_ptr->setError("Exception loading metadata: " + std::string(e.what()));
        return false;
    }
}

bool SqlIndex::refreshMetadata() {
    // Clear existing metadata and reload
    if (m_metadata) {
        m_metadata.reset();
    }
    return loadMetadata();
}

//----------------------------------------------------------------------
// Metadata Accessor Methods
//----------------------------------------------------------------------

SqlIndexType SqlIndex::indexType() const noexcept {
    return metadata()->indexType;
}

SqlIndex& SqlIndex::setIndexType(SqlIndexType indexType) noexcept {
    metadata()->indexType = indexType;

    // Update unique flag based on index type
    if (indexType == SqlIndexType::Primary || indexType == SqlIndexType::Unique) {
        metadata()->isUnique = true;
    }
    return *this;
}

std::string_view SqlIndex::tableName() const noexcept {
    return metadata()->tableName;
}

SqlIndex& SqlIndex::setTableName(std::string tableName) {
    metadata()->tableName = std::move(tableName);
    return *this;
}

std::string_view SqlIndex::comment() const noexcept {
    return metadata()->comment;
}

SqlIndex& SqlIndex::setComment(std::string comment) {
    metadata()->comment = std::move(comment);
    return *this;
}

std::vector<std::string> SqlIndex::columns() const noexcept {
    return metadata()->columns;
}

size_t SqlIndex::columnCount() const noexcept {
    return metadata()->columns.size();
}

SqlIndex& SqlIndex::addColumn(std::string columnName, SqlSortOrder sortOrder) {
    // Check if column already exists
    auto& cols = metadata()->columns;
    auto it = std::find(cols.begin(), cols.end(), columnName);
    if (it != cols.end()) {
        // Update existing column's sort order
        size_t index = std::distance(cols.begin(), it);
        if (index < m_metadata->sortOrders.size()) {
            m_metadata->sortOrders[index] = sortOrder;
        }
    } else {
        // Add new column
        cols.push_back(std::move(columnName));
        m_metadata->sortOrders.push_back(sortOrder);
    }
    return *this;
}

SqlIndex& SqlIndex::removeColumn(std::string_view columnName) {
    auto& cols = metadata()->columns;
    auto it = std::find(cols.begin(), cols.end(), columnName);
    if (it != cols.end()) {
        size_t index = std::distance(cols.begin(), it);

        // Remove from columns vector
        cols.erase(it);

        // Remove corresponding sort order
        if (index < m_metadata->sortOrders.size()) {
            m_metadata->sortOrders.erase(m_metadata->sortOrders.begin() + index);
        }
    }
    return *this;
}

SqlIndex& SqlIndex::clearColumns() {
    metadata()->columns.clear();
    m_metadata->sortOrders.clear();
    return *this;
}

std::vector<SqlSortOrder> SqlIndex::sortOrders() const noexcept {
    return metadata()->sortOrders;
}

SqlIndex& SqlIndex::setSortOrders(std::vector<SqlSortOrder> sortOrders) {
    metadata()->sortOrders = std::move(sortOrders);
    return *this;
}

std::optional<std::string> SqlIndex::whereClause() const noexcept {
    return metadata()->whereClause;
}

SqlIndex& SqlIndex::setWhereClause(std::string_view whereClause) {
    metadata()->whereClause = std::move(whereClause);
    return *this;
}

bool SqlIndex::isUnique() const noexcept {
    return metadata()->isUnique;
}

SqlIndex& SqlIndex::setUnique(bool unique) noexcept {
    metadata()->isUnique = unique;

    // Update index type if setting unique
    if (unique && metadata()->indexType == SqlIndexType::Normal) {
        metadata()->indexType = SqlIndexType::Unique;
    } else if (!unique && metadata()->indexType == SqlIndexType::Unique) {
        metadata()->indexType = SqlIndexType::Normal;
    }
    return *this;
}

bool SqlIndex::isClustered() const noexcept {
    return metadata()->isClustered;
}

SqlIndex& SqlIndex::setClustered(bool clustered) noexcept {
    metadata()->isClustered = clustered;

    // Update index type if setting clustered
    if (clustered) {
        m_metadata->indexType = SqlIndexType::Clustered;
    } else if (m_metadata->indexType == SqlIndexType::Clustered) {
        m_metadata->indexType = SqlIndexType::NonClustered;
    }
    return *this;
}

bool SqlIndex::isVisible() const noexcept {
    return metadata()->isVisible;
}

SqlIndex& SqlIndex::setVisible(bool visible) noexcept {
    metadata()->isVisible = visible;
    return *this;
}

bool SqlIndex::isSystem() const noexcept {
    return metadata()->isSystem;
}

SqlIndex& SqlIndex::setSystem(bool system) noexcept {
    metadata()->isSystem = system;
    return *this;
}

bool SqlIndex::isPrimaryKey() const noexcept {
    return metadata()->indexType == SqlIndexType::Primary;
}

std::optional<size_t> SqlIndex::fillFactor() const noexcept {
    return metadata()->fillfactor;
}

SqlIndex& SqlIndex::setFillFactor(std::optional<size_t> fillFactor) noexcept {
    metadata()->fillfactor = fillFactor;
    return *this;
}

//----------------------------------------------------------------------
// Database Interaction Operations
//----------------------------------------------------------------------

SqlTable SqlIndex::table() const noexcept {
    if (d_ptr) {
        return d_ptr->table;
    }

    // Return a table constructed from metadata
    if (m_metadata && !m_metadata->tableName.empty()) {
        return SqlTable(m_metadata->tableName);
    }

    return SqlTable{};
}

SqlIndex& SqlIndex::setTable(const SqlTable& table) {
    // Initialize d_ptr if needed
    if (!d_ptr) {
        d_ptr = std::make_shared<SqlIndexPrivate>();
    }

    d_ptr->table = table;

    // Update metadata
    metadata()->tableName = table.name();

    // Set up database connection if table has one
    auto db = table.database();
    if (db) {
        d_ptr->setDatabase(db);
    }
    return *this;
}

std::shared_ptr<SqlDatabase> SqlIndex::database() const {
    if (d_ptr && d_ptr->database) {
        return d_ptr->database;
    }

    // Try to get database from associated table
    auto tbl = table();
    return tbl.database();
}

void SqlIndex::setDatabase(std::shared_ptr<SqlDatabase> db) {
    if (!d_ptr) {
        d_ptr = std::make_shared<SqlIndexPrivate>();
    }
    d_ptr->setDatabase(db);
}

bool SqlIndex::exists() const {
    if (!d_ptr || !d_ptr->isValid()) {
        return false;
    }

    try {
        SqlQuery query(*d_ptr->database);

        // Query to check if index exists
        std::string sql = "SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS WHERE INDEX_NAME = ? AND TABLE_NAME = ?";

        query.setQuery(sql).prepare();
        query.bind(1, std::string(name()))
            .bind(2, metadata()->tableName);

        if (!query.execute() || !query.next()) {
            return false;
        }

        return query.value(0).to<int>() > 0;

    } catch (const std::exception&) {
        return false;
    }
}

bool SqlIndex::create(bool ifNotExists) {
    if (!d_ptr || !d_ptr->isValid()) {
        d_ptr->setError("No database connection available");
        return false;
    }

    try {
        SqlQuery query(*d_ptr->database);

        // Build CREATE INDEX statement
        std::string sql = createSql(ifNotExists);

        query.setQuery(sql);

        if (!query.execute()) {
            d_ptr->setError(std::format("Failed to create index: {}", query.lastError().message()));
            return false;
        }

        d_ptr->clearError();
        return true;

    } catch (const std::exception& e) {
        d_ptr->setError("Exception creating index: " + std::string(e.what()));
        return false;
    }
}

bool SqlIndex::drop(bool ifExists) {
    if (!d_ptr || !d_ptr->isValid()) {
        d_ptr->setError("No database connection available");
        return false;
    }

    try {
        SqlQuery query(*d_ptr->database);

        // Build DROP INDEX statement
        std::string sql = dropSql(ifExists);

        query.setQuery(sql);

        if (!query.execute()) {
            d_ptr->setError(std::format("Failed to drop index: {}", query.lastError().message()));
            return false;
        }

        d_ptr->clearError();
        return true;

    } catch (const std::exception& e) {
        d_ptr->setError("Exception dropping index: " + std::string(e.what()));
        return false;
    }
}

bool SqlIndex::rebuild() {
    if (!d_ptr || !d_ptr->isValid()) {
        d_ptr->setError("No database connection available");
        return false;
    }

    try {
        SqlQuery query(*d_ptr->database);

        // Build REBUILD INDEX statement (database-specific)
        std::string sql = "ALTER INDEX " + qualifiedName() + " REBUILD";

        query.setQuery(sql);

        if (!query.execute()) {
            d_ptr->setError(std::format("Failed to rebuild index: {}", query.lastError().message()));
            return false;
        }

        d_ptr->clearError();
        return true;

    } catch (const std::exception& e) {
        d_ptr->setError("Exception rebuilding index: " + std::string(e.what()));
        return false;
    }
}

bool SqlIndex::analyze() {
    if (!d_ptr || !d_ptr->isValid()) {
        d_ptr->setError("No database connection available");
        return false;
    }

    try {
        SqlQuery query(*d_ptr->database);

        // Build ANALYZE INDEX statement
        std::string sql = "ANALYZE TABLE " + metadata()->tableName;

        query.setQuery(sql);

        if (!query.execute()) {
            d_ptr->setError(std::format("Failed to analyze index: {}", query.lastError().message()));
            return false;
        }

        d_ptr->clearError();
        return true;

    } catch (const std::exception& e) {
        d_ptr->setError("Exception analyzing index: " + std::string(e.what()));
        return false;
    }
}

//----------------------------------------------------------------------
// Statistics and Usage Methods
//----------------------------------------------------------------------

size_t SqlIndex::sizeBytes() const {
    if (!d_ptr || !d_ptr->isValid()) {
        return 0;
    }

    try {
        SqlQuery query(*d_ptr->database);

        // Query for index size (database-specific)
        std::string sql = "SELECT SUM(stat_value) FROM mysql.innodb_index_stats WHERE index_name = ? AND table_name = ? AND stat_name = 'size'";

        query.setQuery(sql).prepare();
        query.bind(1, std::string(name()))
            .bind(2, metadata()->tableName);

        if (!query.execute() || !query.next()) {
            return 0;
        }

        return query.value(0).to<size_t>();

    } catch (const std::exception&) {
        return 0;
    }
}

size_t SqlIndex::pageCount() const {
    if (!d_ptr || !d_ptr->isValid()) {
        // Return cached value if available
        if (metadata()->pages.has_value()) {
            return metadata()->pages.value();
        }
        return 0;
    }

    try {
        SqlQuery query(*d_ptr->database);

        // Query for index page count
        std::string sql = "SELECT SUM(stat_value) FROM mysql.innodb_index_stats WHERE index_name = ? AND table_name = ? AND stat_name = 'n_leaf_pages'";

        query.setQuery(sql).prepare();
        query.bind(1, std::string(name()))
            .bind(2, metadata()->tableName);

        if (!query.execute() || !query.next()) {
            return 0;
        }

        return query.value(0).to<size_t>();

    } catch (const std::exception&) {
        return 0;
    }
}

size_t SqlIndex::rowCount() const {
    if (!d_ptr || !d_ptr->isValid()) {
        // Return cached value if available
        if (metadata()->rows.has_value()) {
            return metadata()->rows.value();
        }
        return 0;
    }

    try {
        SqlQuery query(*d_ptr->database);

        // Query for index row count
        std::string sql = "SELECT SUM(stat_value) FROM mysql.innodb_index_stats WHERE index_name = ? AND table_name = ? AND stat_name = 'n_diff_pfx01'";

        query.setQuery(sql).prepare();
        query.bind(1, std::string(name()))
            .bind(2, metadata()->tableName);

        if (!query.execute() || !query.next()) {
            return 0;
        }

        return query.value(0).to<size_t>();

    } catch (const std::exception&) {
        return 0;
    }
}

std::optional<double> SqlIndex::selectivity() const {
    if (!d_ptr || !d_ptr->isValid()) {
        // Return cached value if available
        return metadata()->selectivity;
    }

    try {
        SqlQuery query(*d_ptr->database);

        // Calculate selectivity as distinct values / total rows
        std::string sql = "SELECT (SELECT COUNT(DISTINCT " + metadata()->columns[0] + ") FROM " + metadata()->tableName + ") / "
                                                                                                                        "(SELECT COUNT(*) FROM "
                          + metadata()->tableName + ") AS selectivity";

        query.setQuery(sql);

        if (!query.execute() || !query.next()) {
            return std::nullopt;
        }

        return query.value(0).to<double>();

    } catch (const std::exception&) {
        return std::nullopt;
    }
}

bool SqlIndex::isUsed() const {
    if (!d_ptr || !d_ptr->isValid()) {
        return false;
    }

    try {
        SqlQuery query(*d_ptr->database);

        // Check if index is used in query plans (database-specific)
        std::string sql = "SELECT COUNT(*) FROM performance_schema.events_statements_summary_by_digest "
                          "WHERE DIGEST_TEXT LIKE '%"
                          + metadata()->tableName + "%' AND DIGEST_TEXT LIKE '%" + std::string(name()) + "%'";

        query.setQuery(sql);

        if (!query.execute() || !query.next()) {
            return false;
        }

        return query.value(0).to<int>() > 0;

    } catch (const std::exception&) {
        return false;
    }
}

std::unordered_map<std::string, std::string> SqlIndex::usageStatistics() const {
    std::unordered_map<std::string, std::string> stats;

    if (!d_ptr || !d_ptr->isValid()) {
        return stats;
    }

    try {
        SqlQuery query(*d_ptr->database);

        // Get various index statistics
        std::string sql = "SELECT stat_name, stat_value FROM mysql.innodb_index_stats WHERE index_name = ? AND table_name = ?";

        query.setQuery(sql).prepare();
        query.bind(1, std::string(name()))
             .bind(2, metadata()->tableName);

        if (!query.execute()) {
            return stats;
        }

        while (query.next()) {
            std::string statName = query.value("stat_name").to<std::string>();
            std::string statValue = query.value("stat_value").to<std::string>();
            stats[statName] = statValue;
        }

    } catch (const std::exception&) {
        // Return empty map on error
    }

    return stats;
}

//----------------------------------------------------------------------
// SQL Generation Methods
//----------------------------------------------------------------------

std::string SqlIndex::qualifiedName() const {
    // For indexes, qualified name is typically just the index name
    // Some databases might require table.index_name format
    return std::string(name());
}
std::string SqlIndex::createSql(bool ifNotExists) const {
    std::ostringstream sql;

    // Handle different index types
    switch (metadata()->indexType) {
    case SqlIndexType::Primary:
        sql << "ALTER TABLE " << metadata()->tableName << " ADD PRIMARY KEY (";
        break;
    case SqlIndexType::Unique:
        sql << "CREATE UNIQUE INDEX ";
        if (ifNotExists) {
            sql << "IF NOT EXISTS ";
        }
        sql << name() << " ON " << metadata()->tableName << " (";
        break;
    case SqlIndexType::FullText:
        sql << "CREATE FULLTEXT INDEX ";
        if (ifNotExists) {
            sql << "IF NOT EXISTS ";
        }
        sql << name() << " ON " << metadata()->tableName << " (";
        break;
    default:
        sql << "CREATE INDEX ";
        if (ifNotExists) {
            sql << "IF NOT EXISTS ";
        }
        sql << name() << " ON " << metadata()->tableName << " (";
        break;
    }

    // Add columns with sort orders
    const auto& columns = metadata()->columns;
    const auto& sortOrders = metadata()->sortOrders;

    if (columns.empty()) {
        // If no columns specified, this is an error condition
        sql << "/* ERROR: No columns specified for index */";
    } else {
        for (size_t i = 0; i < columns.size(); ++i) {
            if (i > 0) sql << ", ";
            sql << columns[i];

            // Add sort order if specified and not default
            if (i < sortOrders.size() && sortOrders[i] == SqlSortOrder::Descending) {
                sql << " DESC";
            }
        }
    }

    sql << ")";

    // Add WHERE clause for partial indexes
    if (metadata()->whereClause.has_value() && !metadata()->whereClause->empty()) {
        sql << " WHERE " << metadata()->whereClause.value();
    }

    // Add fill factor if specified
    if (metadata()->fillfactor.has_value()) {
        sql << " WITH FILLFACTOR = " << metadata()->fillfactor.value();
    }

    // Add comment if specified
    if (!metadata()->comment.empty()) {
        sql << " COMMENT '" << metadata()->comment << "'";
    }

    return sql.str();
}

std::string SqlIndex::dropSql(bool ifExists) const {
    std::ostringstream sql;

    if (metadata()->indexType == SqlIndexType::Primary) {
        sql << "ALTER TABLE " << metadata()->tableName << " DROP PRIMARY KEY";
    } else {
        sql << "DROP INDEX ";
        if (ifExists) {
            sql << "IF EXISTS ";
        }
        sql << name() << " ON " << metadata()->tableName;
    }

    return sql.str();
}

} // namespace database 
