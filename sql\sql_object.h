#ifndef DATABASE_SQL_OBJECT_H
#define DATABASE_SQL_OBJECT_H

#include <memory>
#include <string>
#include <string_view>

#include "sql_enums.h"

namespace database {

// Forward declarations
class SqlDriver;
class SqlDatabase;

/**
 * @brief Base class for all SQL database objects
 */
class SqlObject {
public:
    SqlObject() noexcept = default;
    explicit SqlObject(std::string_view name) noexcept;
    SqlObject(std::string_view name, SqlObjectType type) noexcept;
    virtual ~SqlObject() = default;

    // Copy and move semantics
    SqlObject(const SqlObject& other) = default;
    SqlObject(SqlObject&& other) noexcept = default;
    SqlObject& operator=(const SqlObject& other) = default;
    SqlObject& operator=(SqlObject&& other) noexcept = default;

    // Basic Properties
    [[nodiscard]] std::string_view name() const noexcept;
    void setName(std::string_view name) noexcept;

    [[nodiscard]] SqlObjectType objectType() const noexcept { return m_type; }
    [[nodiscard]] bool isValid() const noexcept { return !m_name.empty(); }

    [[nodiscard]] virtual std::string qualifiedName() const = 0;
    [[nodiscard]] virtual std::string toSql() const = 0;

    // Enhanced comparison operators
    [[nodiscard]] bool operator==(const SqlObject& other) const noexcept {
        return m_name == other.m_name && m_type == other.m_type;
    }

    [[nodiscard]] bool operator!=(const SqlObject& other) const noexcept {
        return !(*this == other);
    }

    [[nodiscard]] bool operator<(const SqlObject& other) const noexcept {
        if (m_name != other.m_name) return m_name < other.m_name;
        return m_type < other.m_type;
    }

    // Hash support for unordered containers
    [[nodiscard]] size_t hash() const noexcept {
        return std::hash<std::string>{}(m_name) ^
               (static_cast<size_t>(m_type) << 1);
    }

protected:
    void setObjectType(SqlObjectType type) noexcept { m_type = type; }

    [[nodiscard]] virtual std::shared_ptr<SqlDatabase> database() const = 0;
    void setDatabase(std::shared_ptr<SqlDatabase> db) {};

    // [[nodiscard]] virtual bool hasMetadata() const noexcept { return false; }
    // virtual bool loadMetadata() { return false; }
    // virtual bool refreshMetadata() { return false; }

private:
    std::string m_name;
    SqlObjectType m_type = SqlObjectType::Unknown;
};

} // namespace database

// Hash specialization for SqlObject to use in unordered containers
namespace std {
template<>
struct hash<database::SqlObject> {
    [[nodiscard]] size_t operator()(const database::SqlObject& obj) const noexcept {
        return obj.hash();
    }
};
} // namespace std

#endif // DATABASE_SQL_OBJECT_H
