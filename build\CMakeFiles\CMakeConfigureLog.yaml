
---
events:
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.28/Modules/CMakeDetermineSystem.cmake:233 (message)"
      - "CMakeLists.txt:2 (project)"
    message: |
      The system is: Windows - 10.0.22631 - AMD64
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.28/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-3.28/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-3.28/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
      Compiler:  
      Build flags: 
      Id flags:  
      
      The output was:
      0
      閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.13.26+f3741e9cf
      鐢熸垚鍚姩鏃堕棿涓?2025/5/29 16:22:45銆?
      
      鑺傜偣 1 涓婄殑椤圭洰鈥淓:\\Projects\\Code\\AI Agent\\database_v13\\build\\CMakeFiles\\3.28.1\\CompilerIdCXX\\CompilerIdCXX.vcxproj鈥?榛樿鐩爣)銆?
      PrepareForBuild:
        姝ｅ湪鍒涘缓鐩綍鈥淒ebug\\鈥濄€?
        宸插惎鐢ㄧ粨鏋勫寲杈撳嚭銆傜紪璇戝櫒璇婃柇鐨勬牸寮忚缃皢鍙嶆槧閿欒灞傛缁撴瀯銆傛湁鍏宠缁嗕俊鎭紝璇峰弬闃?https://aka.ms/cpp/structured-output銆?
        姝ｅ湪鍒涘缓鐩綍鈥淒ebug\\CompilerIdCXX.tlog\\鈥濄€?
      InitializeBuildStatus:
        姝ｅ湪鍒涘缓鈥淒ebug\\CompilerIdCXX.tlog\\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?
        姝ｅ湪瀵光€淒ebug\\CompilerIdCXX.tlog\\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?
      VcpkgTripletSelection:
        Using triplet "x64-windows" from "E:\\Projects\\Code\\vcpkg\\installed\\x64-windows\\"
        Using normalized configuration "Release"
      ClCompile:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\CL.exe /c /I"E:\\Projects\\Code\\vcpkg\\installed\\x64-windows\\include" /nologo /W0 /WX- /diagnostics:column /Od /D _MBCS /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"Debug\\\\" /Fd"Debug\\vc143.pdb" /external:W0 /Gd /TP /FC /errorReport:queue CMakeCXXCompilerId.cpp
        CMakeCXXCompilerId.cpp
      Link:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:".\\CompilerIdCXX.exe" /INCREMENTAL:NO /NOLOGO /LIBPATH:"E:\\Projects\\Code\\vcpkg\\installed\\x64-windows\\lib" /LIBPATH:"E:\\Projects\\Code\\vcpkg\\installed\\x64-windows\\lib\\manual-link" kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib "E:\\Projects\\Code\\vcpkg\\installed\\x64-windows\\lib\\*.lib" /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /PDB:".\\CompilerIdCXX.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:".\\CompilerIdCXX.lib" /MACHINE:X64 Debug\\CMakeCXXCompilerId.obj
        CompilerIdCXX.vcxproj -> E:\\Projects\\Code\\AI Agent\\database_v13\\build\\CMakeFiles\\3.28.1\\CompilerIdCXX\\CompilerIdCXX.exe
      AppLocalFromInstalled:
        pwsh.exe -ExecutionPolicy Bypass -noprofile -File "E:\\Projects\\Code\\vcpkg\\scripts\\buildsystems\\msbuild\\applocal.ps1" "E:\\Projects\\Code\\AI Agent\\database_v13\\build\\CMakeFiles\\3.28.1\\CompilerIdCXX\\CompilerIdCXX.exe" "E:\\Projects\\Code\\vcpkg\\installed\\x64-windows\\bin" "Debug\\CompilerIdCXX.tlog\\CompilerIdCXX.write.1u.tlog" "Debug\\vcpkg.applocal.log"
        'pwsh.exe' 涓嶆槸鍐呴儴鎴栧閮ㄥ懡浠わ紝涔熶笉鏄彲杩愯鐨勭▼搴?
        鎴栨壒澶勭悊鏂囦欢銆?
        鍛戒护鈥減wsh.exe -ExecutionPolicy Bypass -noprofile -File "E:\\Projects\\Code\\vcpkg\\scripts\\buildsystems\\msbuild\\applocal.ps1" "E:\\Projects\\Code\\AI Agent\\database_v13\\build\\CMakeFiles\\3.28.1\\CompilerIdCXX\\CompilerIdCXX.exe" "E:\\Projects\\Code\\vcpkg\\installed\\x64-windows\\bin" "Debug\\CompilerIdCXX.tlog\\CompilerIdCXX.write.1u.tlog" "Debug\\vcpkg.applocal.log"鈥濆凡閫€鍑猴紝浠ｇ爜涓?9009銆?
        "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\powershell.exe" -ExecutionPolicy Bypass -noprofile -File "E:\\Projects\\Code\\vcpkg\\scripts\\buildsystems\\msbuild\\applocal.ps1" "E:\\Projects\\Code\\AI Agent\\database_v13\\build\\CMakeFiles\\3.28.1\\CompilerIdCXX\\CompilerIdCXX.exe" "E:\\Projects\\Code\\vcpkg\\installed\\x64-windows\\bin" "Debug\\CompilerIdCXX.tlog\\CompilerIdCXX.write.1u.tlog" "Debug\\vcpkg.applocal.log"
      PostBuildEvent:
        for %%i in (cl.exe) do @echo CMAKE_CXX_COMPILER=%%~$PATH:i
        :VCEnd
        CMAKE_CXX_COMPILER=C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\Hostx64\\x64\\cl.exe
      FinalizeBuildStatus:
        姝ｅ湪鍒犻櫎鏂囦欢鈥淒ebug\\CompilerIdCXX.tlog\\unsuccessfulbuild鈥濄€?
        姝ｅ湪瀵光€淒ebug\\CompilerIdCXX.tlog\\CompilerIdCXX.lastbuildstate鈥濇墽琛?Touch 浠诲姟銆?
      宸插畬鎴愮敓鎴愰」鐩€淓:\\Projects\\Code\\AI Agent\\database_v13\\build\\CMakeFiles\\3.28.1\\CompilerIdCXX\\CompilerIdCXX.vcxproj鈥?榛樿鐩爣)鐨勬搷浣溿€?
      
      宸叉垚鍔熺敓鎴愩€?
          0 涓鍛?
          0 涓敊璇?
      
      宸茬敤鏃堕棿 00:00:03.19
      
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.exe"
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.vcxproj"
      
      The CXX compiler identification is MSVC, found in:
        E:/Projects/Code/AI Agent/database_v13/build/CMakeFiles/3.28.1/CompilerIdCXX/CompilerIdCXX.exe
      
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.28/Modules/CMakeDetermineCompilerABI.cmake:57 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.28/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    checks:
      - "Detecting CXX compiler ABI info"
    directories:
      source: "E:/Projects/Code/AI Agent/database_v13/build/CMakeFiles/CMakeScratch/TryCompile-0rzdzb"
      binary: "E:/Projects/Code/AI Agent/database_v13/build/CMakeFiles/CMakeScratch/TryCompile-0rzdzb"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /W3 /GR /EHsc"
      CMAKE_CXX_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "CMAKE_CXX_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'E:/Projects/Code/AI Agent/database_v13/build/CMakeFiles/CMakeScratch/TryCompile-0rzdzb'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_4648f.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.13.26+f3741e9cf
        鐢熸垚鍚姩鏃堕棿涓?2025/5/29 16:22:48銆?
        
        鑺傜偣 1 涓婄殑椤圭洰鈥淓:\\Projects\\Code\\AI Agent\\database_v13\\build\\CMakeFiles\\CMakeScratch\\TryCompile-0rzdzb\\cmTC_4648f.vcxproj鈥?榛樿鐩爣)銆?
        PrepareForBuild:
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_4648f.dir\\Debug\\鈥濄€?
          宸插惎鐢ㄧ粨鏋勫寲杈撳嚭銆傜紪璇戝櫒璇婃柇鐨勬牸寮忚缃皢鍙嶆槧閿欒灞傛缁撴瀯銆傛湁鍏宠缁嗕俊鎭紝璇峰弬闃?https://aka.ms/cpp/structured-output銆?
          姝ｅ湪鍒涘缓鐩綍鈥淓:\\Projects\\Code\\AI Agent\\database_v13\\build\\CMakeFiles\\CMakeScratch\\TryCompile-0rzdzb\\Debug\\鈥濄€?
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_4648f.dir\\Debug\\cmTC_4648f.tlog\\鈥濄€?
        InitializeBuildStatus:
          姝ｅ湪鍒涘缓鈥渃mTC_4648f.dir\\Debug\\cmTC_4648f.tlog\\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?
          姝ｅ湪瀵光€渃mTC_4648f.dir\\Debug\\cmTC_4648f.tlog\\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?
        VcpkgTripletSelection:
          Using triplet "x64-windows" from "E:\\Projects\\Code\\vcpkg\\installed\\x64-windows\\"
          Using normalized configuration "Release"
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\CL.exe /c /I"E:\\Projects\\Code\\vcpkg\\installed\\x64-windows\\include" /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /Fo"cmTC_4648f.dir\\Debug\\\\" /Fd"cmTC_4648f.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TP /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-3.28\\Modules\\CMakeCXXCompilerABI.cpp"
          鐢ㄤ簬 x64 鐨?Microsoft (R) C/C++ 浼樺寲缂栬瘧鍣?19.43.34810 鐗?
          鐗堟潈鎵€鏈?C) Microsoft Corporation銆備繚鐣欐墍鏈夋潈鍒┿€?
          cl /c /I"E:\\Projects\\Code\\vcpkg\\installed\\x64-windows\\include" /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /Fo"cmTC_4648f.dir\\Debug\\\\" /Fd"cmTC_4648f.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TP /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-3.28\\Modules\\CMakeCXXCompilerABI.cpp"
          CMakeCXXCompilerABI.cpp
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"E:\\Projects\\Code\\AI Agent\\database_v13\\build\\CMakeFiles\\CMakeScratch\\TryCompile-0rzdzb\\Debug\\cmTC_4648f.exe" /INCREMENTAL /ILK:"cmTC_4648f.dir\\Debug\\cmTC_4648f.ilk" /NOLOGO /LIBPATH:"E:\\Projects\\Code\\vcpkg\\installed\\x64-windows\\lib" /LIBPATH:"E:\\Projects\\Code\\vcpkg\\installed\\x64-windows\\lib\\manual-link" kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib "E:\\Projects\\Code\\vcpkg\\installed\\x64-windows\\lib\\*.lib" /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"E:/Projects/Code/AI Agent/database_v13/build/CMakeFiles/CMakeScratch/TryCompile-0rzdzb/Debug/cmTC_4648f.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"E:/Projects/Code/AI Agent/database_v13/build/CMakeFiles/CMakeScratch/TryCompile-0rzdzb/Debug/cmTC_4648f.lib" /MACHINE:X64  /machine:x64 cmTC_4648f.dir\\Debug\\CMakeCXXCompilerABI.obj
          cmTC_4648f.vcxproj -> E:\\Projects\\Code\\AI Agent\\database_v13\\build\\CMakeFiles\\CMakeScratch\\TryCompile-0rzdzb\\Debug\\cmTC_4648f.exe
        AppLocalFromInstalled:
          pwsh.exe -ExecutionPolicy Bypass -noprofile -File "E:\\Projects\\Code\\vcpkg\\scripts\\buildsystems\\msbuild\\applocal.ps1" "E:\\Projects\\Code\\AI Agent\\database_v13\\build\\CMakeFiles\\CMakeScratch\\TryCompile-0rzdzb\\Debug\\cmTC_4648f.exe" "E:\\Projects\\Code\\vcpkg\\installed\\x64-windows\\bin" "cmTC_4648f.dir\\Debug\\cmTC_4648f.tlog\\cmTC_4648f.write.1u.tlog" "cmTC_4648f.dir\\Debug\\vcpkg.applocal.log"
          'pwsh.exe' 涓嶆槸鍐呴儴鎴栧閮ㄥ懡浠わ紝涔熶笉鏄彲杩愯鐨勭▼搴?
          鎴栨壒澶勭悊鏂囦欢銆?
          鍛戒护鈥減wsh.exe -ExecutionPolicy Bypass -noprofile -File "E:\\Projects\\Code\\vcpkg\\scripts\\buildsystems\\msbuild\\applocal.ps1" "E:\\Projects\\Code\\AI Agent\\database_v13\\build\\CMakeFiles\\CMakeScratch\\TryCompile-0rzdzb\\Debug\\cmTC_4648f.exe" "E:\\Projects\\Code\\vcpkg\\installed\\x64-windows\\bin" "cmTC_4648f.dir\\Debug\\cmTC_4648f.tlog\\cmTC_4648f.write.1u.tlog" "cmTC_4648f.dir\\Debug\\vcpkg.applocal.log"鈥濆凡閫€鍑猴紝浠ｇ爜涓?9009銆?
          "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\powershell.exe" -ExecutionPolicy Bypass -noprofile -File "E:\\Projects\\Code\\vcpkg\\scripts\\buildsystems\\msbuild\\applocal.ps1" "E:\\Projects\\Code\\AI Agent\\database_v13\\build\\CMakeFiles\\CMakeScratch\\TryCompile-0rzdzb\\Debug\\cmTC_4648f.exe" "E:\\Projects\\Code\\vcpkg\\installed\\x64-windows\\bin" "cmTC_4648f.dir\\Debug\\cmTC_4648f.tlog\\cmTC_4648f.write.1u.tlog" "cmTC_4648f.dir\\Debug\\vcpkg.applocal.log"
        FinalizeBuildStatus:
          姝ｅ湪鍒犻櫎鏂囦欢鈥渃mTC_4648f.dir\\Debug\\cmTC_4648f.tlog\\unsuccessfulbuild鈥濄€?
          姝ｅ湪瀵光€渃mTC_4648f.dir\\Debug\\cmTC_4648f.tlog\\cmTC_4648f.lastbuildstate鈥濇墽琛?Touch 浠诲姟銆?
        宸插畬鎴愮敓鎴愰」鐩€淓:\\Projects\\Code\\AI Agent\\database_v13\\build\\CMakeFiles\\CMakeScratch\\TryCompile-0rzdzb\\cmTC_4648f.vcxproj鈥?榛樿鐩爣)鐨勬搷浣溿€?
        
        宸叉垚鍔熺敓鎴愩€?
            0 涓鍛?
            0 涓敊璇?
        
        宸茬敤鏃堕棿 00:00:00.87
        
      exitCode: 0
...

---
events:
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.28/Modules/Internal/CheckSourceCompiles.cmake:101 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.28/Modules/CheckCXXSourceCompiles.cmake:52 (cmake_check_source_compiles)"
      - "C:/Program Files/CMake/share/cmake-3.28/Modules/FindThreads.cmake:99 (CHECK_CXX_SOURCE_COMPILES)"
      - "C:/Program Files/CMake/share/cmake-3.28/Modules/FindThreads.cmake:163 (_threads_check_libc)"
      - "C:/Program Files/CMake/share/cmake-3.28/Modules/FindGTest.cmake:283 (find_package)"
      - "tests/CMakeLists.txt:6 (find_package)"
    directories:
      source: "E:/Projects/Code/AI Agent/database_v13/build/CMakeFiles/CMakeScratch/TryCompile-tccu5z"
      binary: "E:/Projects/Code/AI Agent/database_v13/build/CMakeFiles/CMakeScratch/TryCompile-tccu5z"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /W3 /GR /EHsc"
      CMAKE_CXX_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "CMAKE_HAVE_LIBC_PTHREAD"
      cached: true
      stdout: |
        Change Dir: 'E:/Projects/Code/AI Agent/database_v13/build/CMakeFiles/CMakeScratch/TryCompile-tccu5z'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_4386c.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.13.26+f3741e9cf
        鐢熸垚鍚姩鏃堕棿涓?2025/5/29 16:23:58銆?
        
        鑺傜偣 1 涓婄殑椤圭洰鈥淓:\\Projects\\Code\\AI Agent\\database_v13\\build\\CMakeFiles\\CMakeScratch\\TryCompile-tccu5z\\cmTC_4386c.vcxproj鈥?榛樿鐩爣)銆?
        PrepareForBuild:
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_4386c.dir\\Debug\\鈥濄€?
          宸插惎鐢ㄧ粨鏋勫寲杈撳嚭銆傜紪璇戝櫒璇婃柇鐨勬牸寮忚缃皢鍙嶆槧閿欒灞傛缁撴瀯銆傛湁鍏宠缁嗕俊鎭紝璇峰弬闃?https://aka.ms/cpp/structured-output銆?
          姝ｅ湪鍒涘缓鐩綍鈥淓:\\Projects\\Code\\AI Agent\\database_v13\\build\\CMakeFiles\\CMakeScratch\\TryCompile-tccu5z\\Debug\\鈥濄€?
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_4386c.dir\\Debug\\cmTC_4386c.tlog\\鈥濄€?
        InitializeBuildStatus:
          姝ｅ湪鍒涘缓鈥渃mTC_4386c.dir\\Debug\\cmTC_4386c.tlog\\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?
          姝ｅ湪瀵光€渃mTC_4386c.dir\\Debug\\cmTC_4386c.tlog\\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?
        VcpkgTripletSelection:
          Using triplet "x64-windows" from "E:\\Projects\\Code\\vcpkg\\installed\\x64-windows\\"
          Using normalized configuration "Release"
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\CL.exe /c /I"E:\\Projects\\Code\\vcpkg\\installed\\x64-windows\\include" /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CMAKE_HAVE_LIBC_PTHREAD /D "CMAKE_INTDIR=\\"Debug\\"" /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++20 /Fo"cmTC_4386c.dir\\Debug\\\\" /Fd"cmTC_4386c.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TP /errorReport:queue "E:\\Projects\\Code\\AI Agent\\database_v13\\build\\CMakeFiles\\CMakeScratch\\TryCompile-tccu5z\\src.cxx"
          鐢ㄤ簬 x64 鐨?Microsoft (R) C/C++ 浼樺寲缂栬瘧鍣?19.43.34810 鐗?
          鐗堟潈鎵€鏈?C) Microsoft Corporation銆備繚鐣欐墍鏈夋潈鍒┿€?
          cl /c /I"E:\\Projects\\Code\\vcpkg\\installed\\x64-windows\\include" /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CMAKE_HAVE_LIBC_PTHREAD /D "CMAKE_INTDIR=\\"Debug\\"" /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++20 /Fo"cmTC_4386c.dir\\Debug\\\\" /Fd"cmTC_4386c.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TP /errorReport:queue "E:\\Projects\\Code\\AI Agent\\database_v13\\build\\CMakeFiles\\CMakeScratch\\TryCompile-tccu5z\\src.cxx"
          src.cxx
        E:\\Projects\\Code\\AI Agent\\database_v13\\build\\CMakeFiles\\CMakeScratch\\TryCompile-tccu5z\\src.cxx(1,10): error C1083: 鏃犳硶鎵撳紑鍖呮嫭鏂囦欢: 鈥減thread.h鈥? No such file or directory [E:\\Projects\\Code\\AI Agent\\database_v13\\build\\CMakeFiles\\CMakeScratch\\TryCompile-tccu5z\\cmTC_4386c.vcxproj]
        宸插畬鎴愮敓鎴愰」鐩€淓:\\Projects\\Code\\AI Agent\\database_v13\\build\\CMakeFiles\\CMakeScratch\\TryCompile-tccu5z\\cmTC_4386c.vcxproj鈥?榛樿鐩爣)鐨勬搷浣?- 澶辫触銆?
        
        鐢熸垚澶辫触銆?
        
        鈥淓:\\Projects\\Code\\AI Agent\\database_v13\\build\\CMakeFiles\\CMakeScratch\\TryCompile-tccu5z\\cmTC_4386c.vcxproj鈥?榛樿鐩爣) (1) ->
        (ClCompile 鐩爣) -> 
          E:\\Projects\\Code\\AI Agent\\database_v13\\build\\CMakeFiles\\CMakeScratch\\TryCompile-tccu5z\\src.cxx(1,10): error C1083: 鏃犳硶鎵撳紑鍖呮嫭鏂囦欢: 鈥減thread.h鈥? No such file or directory [E:\\Projects\\Code\\AI Agent\\database_v13\\build\\CMakeFiles\\CMakeScratch\\TryCompile-tccu5z\\cmTC_4386c.vcxproj]
        
            0 涓鍛?
            1 涓敊璇?
        
        宸茬敤鏃堕棿 00:00:00.57
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.28/Modules/CheckLibraryExists.cmake:69 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.28/Modules/FindThreads.cmake:112 (CHECK_LIBRARY_EXISTS)"
      - "C:/Program Files/CMake/share/cmake-3.28/Modules/FindThreads.cmake:175 (_threads_check_lib)"
      - "C:/Program Files/CMake/share/cmake-3.28/Modules/FindGTest.cmake:283 (find_package)"
      - "tests/CMakeLists.txt:6 (find_package)"
    directories:
      source: "E:/Projects/Code/AI Agent/database_v13/build/CMakeFiles/CMakeScratch/TryCompile-958ipz"
      binary: "E:/Projects/Code/AI Agent/database_v13/build/CMakeFiles/CMakeScratch/TryCompile-958ipz"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /W3 /GR /EHsc"
      CMAKE_CXX_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "CMAKE_HAVE_PTHREADS_CREATE"
      cached: true
      stdout: |
        Change Dir: 'E:/Projects/Code/AI Agent/database_v13/build/CMakeFiles/CMakeScratch/TryCompile-958ipz'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_d7a22.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.13.26+f3741e9cf
        鐢熸垚鍚姩鏃堕棿涓?2025/5/29 16:23:59銆?
        
        鑺傜偣 1 涓婄殑椤圭洰鈥淓:\\Projects\\Code\\AI Agent\\database_v13\\build\\CMakeFiles\\CMakeScratch\\TryCompile-958ipz\\cmTC_d7a22.vcxproj鈥?榛樿鐩爣)銆?
        PrepareForBuild:
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_d7a22.dir\\Debug\\鈥濄€?
          宸插惎鐢ㄧ粨鏋勫寲杈撳嚭銆傜紪璇戝櫒璇婃柇鐨勬牸寮忚缃皢鍙嶆槧閿欒灞傛缁撴瀯銆傛湁鍏宠缁嗕俊鎭紝璇峰弬闃?https://aka.ms/cpp/structured-output銆?
          姝ｅ湪鍒涘缓鐩綍鈥淓:\\Projects\\Code\\AI Agent\\database_v13\\build\\CMakeFiles\\CMakeScratch\\TryCompile-958ipz\\Debug\\鈥濄€?
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_d7a22.dir\\Debug\\cmTC_d7a22.tlog\\鈥濄€?
        InitializeBuildStatus:
          姝ｅ湪鍒涘缓鈥渃mTC_d7a22.dir\\Debug\\cmTC_d7a22.tlog\\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?
          姝ｅ湪瀵光€渃mTC_d7a22.dir\\Debug\\cmTC_d7a22.tlog\\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?
        VcpkgTripletSelection:
          Using triplet "x64-windows" from "E:\\Projects\\Code\\vcpkg\\installed\\x64-windows\\"
          Using normalized configuration "Release"
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\CL.exe /c /I"E:\\Projects\\Code\\vcpkg\\installed\\x64-windows\\include" /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CHECK_FUNCTION_EXISTS=pthread_create /D "CMAKE_INTDIR=\\"Debug\\"" /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++20 /Fo"cmTC_d7a22.dir\\Debug\\\\" /Fd"cmTC_d7a22.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TP /errorReport:queue "E:\\Projects\\Code\\AI Agent\\database_v13\\build\\CMakeFiles\\CMakeScratch\\TryCompile-958ipz\\CheckFunctionExists.cxx"
          鐢ㄤ簬 x64 鐨?Microsoft (R) C/C++ 浼樺寲缂栬瘧鍣?19.43.34810 鐗?
          鐗堟潈鎵€鏈?C) Microsoft Corporation銆備繚鐣欐墍鏈夋潈鍒┿€?
          cl /c /I"E:\\Projects\\Code\\vcpkg\\installed\\x64-windows\\include" /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CHECK_FUNCTION_EXISTS=pthread_create /D "CMAKE_INTDIR=\\"Debug\\"" /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++20 /Fo"cmTC_d7a22.dir\\Debug\\\\" /Fd"cmTC_d7a22.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TP /errorReport:queue "E:\\Projects\\Code\\AI Agent\\database_v13\\build\\CMakeFiles\\CMakeScratch\\TryCompile-958ipz\\CheckFunctionExists.cxx"
          CheckFunctionExists.cxx
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"E:\\Projects\\Code\\AI Agent\\database_v13\\build\\CMakeFiles\\CMakeScratch\\TryCompile-958ipz\\Debug\\cmTC_d7a22.exe" /INCREMENTAL /ILK:"cmTC_d7a22.dir\\Debug\\cmTC_d7a22.ilk" /NOLOGO /LIBPATH:"E:\\Projects\\Code\\vcpkg\\installed\\x64-windows\\lib" /LIBPATH:"E:\\Projects\\Code\\vcpkg\\installed\\x64-windows\\lib\\manual-link" pthreads.lib kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib "E:\\Projects\\Code\\vcpkg\\installed\\x64-windows\\lib\\*.lib" /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"E:/Projects/Code/AI Agent/database_v13/build/CMakeFiles/CMakeScratch/TryCompile-958ipz/Debug/cmTC_d7a22.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"E:/Projects/Code/AI Agent/database_v13/build/CMakeFiles/CMakeScratch/TryCompile-958ipz/Debug/cmTC_d7a22.lib" /MACHINE:X64  /machine:x64 cmTC_d7a22.dir\\Debug\\CheckFunctionExists.obj
        LINK : fatal error LNK1104: 鏃犳硶鎵撳紑鏂囦欢鈥減threads.lib鈥?[E:\\Projects\\Code\\AI Agent\\database_v13\\build\\CMakeFiles\\CMakeScratch\\TryCompile-958ipz\\cmTC_d7a22.vcxproj]
        宸插畬鎴愮敓鎴愰」鐩€淓:\\Projects\\Code\\AI Agent\\database_v13\\build\\CMakeFiles\\CMakeScratch\\TryCompile-958ipz\\cmTC_d7a22.vcxproj鈥?榛樿鐩爣)鐨勬搷浣?- 澶辫触銆?
        
        鐢熸垚澶辫触銆?
        
        鈥淓:\\Projects\\Code\\AI Agent\\database_v13\\build\\CMakeFiles\\CMakeScratch\\TryCompile-958ipz\\cmTC_d7a22.vcxproj鈥?榛樿鐩爣) (1) ->
        (Link 鐩爣) -> 
          LINK : fatal error LNK1104: 鏃犳硶鎵撳紑鏂囦欢鈥減threads.lib鈥?[E:\\Projects\\Code\\AI Agent\\database_v13\\build\\CMakeFiles\\CMakeScratch\\TryCompile-958ipz\\cmTC_d7a22.vcxproj]
        
            0 涓鍛?
            1 涓敊璇?
        
        宸茬敤鏃堕棿 00:00:00.44
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.28/Modules/CheckLibraryExists.cmake:69 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.28/Modules/FindThreads.cmake:112 (CHECK_LIBRARY_EXISTS)"
      - "C:/Program Files/CMake/share/cmake-3.28/Modules/FindThreads.cmake:176 (_threads_check_lib)"
      - "C:/Program Files/CMake/share/cmake-3.28/Modules/FindGTest.cmake:283 (find_package)"
      - "tests/CMakeLists.txt:6 (find_package)"
    directories:
      source: "E:/Projects/Code/AI Agent/database_v13/build/CMakeFiles/CMakeScratch/TryCompile-oxhjh9"
      binary: "E:/Projects/Code/AI Agent/database_v13/build/CMakeFiles/CMakeScratch/TryCompile-oxhjh9"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /W3 /GR /EHsc"
      CMAKE_CXX_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "CMAKE_HAVE_PTHREAD_CREATE"
      cached: true
      stdout: |
        Change Dir: 'E:/Projects/Code/AI Agent/database_v13/build/CMakeFiles/CMakeScratch/TryCompile-oxhjh9'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_76621.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.13.26+f3741e9cf
        鐢熸垚鍚姩鏃堕棿涓?2025/5/29 16:24:00銆?
        
        鑺傜偣 1 涓婄殑椤圭洰鈥淓:\\Projects\\Code\\AI Agent\\database_v13\\build\\CMakeFiles\\CMakeScratch\\TryCompile-oxhjh9\\cmTC_76621.vcxproj鈥?榛樿鐩爣)銆?
        PrepareForBuild:
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_76621.dir\\Debug\\鈥濄€?
          宸插惎鐢ㄧ粨鏋勫寲杈撳嚭銆傜紪璇戝櫒璇婃柇鐨勬牸寮忚缃皢鍙嶆槧閿欒灞傛缁撴瀯銆傛湁鍏宠缁嗕俊鎭紝璇峰弬闃?https://aka.ms/cpp/structured-output銆?
          姝ｅ湪鍒涘缓鐩綍鈥淓:\\Projects\\Code\\AI Agent\\database_v13\\build\\CMakeFiles\\CMakeScratch\\TryCompile-oxhjh9\\Debug\\鈥濄€?
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_76621.dir\\Debug\\cmTC_76621.tlog\\鈥濄€?
        InitializeBuildStatus:
          姝ｅ湪鍒涘缓鈥渃mTC_76621.dir\\Debug\\cmTC_76621.tlog\\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?
          姝ｅ湪瀵光€渃mTC_76621.dir\\Debug\\cmTC_76621.tlog\\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?
        VcpkgTripletSelection:
          Using triplet "x64-windows" from "E:\\Projects\\Code\\vcpkg\\installed\\x64-windows\\"
          Using normalized configuration "Release"
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\CL.exe /c /I"E:\\Projects\\Code\\vcpkg\\installed\\x64-windows\\include" /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CHECK_FUNCTION_EXISTS=pthread_create /D "CMAKE_INTDIR=\\"Debug\\"" /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++20 /Fo"cmTC_76621.dir\\Debug\\\\" /Fd"cmTC_76621.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TP /errorReport:queue "E:\\Projects\\Code\\AI Agent\\database_v13\\build\\CMakeFiles\\CMakeScratch\\TryCompile-oxhjh9\\CheckFunctionExists.cxx"
          鐢ㄤ簬 x64 鐨?Microsoft (R) C/C++ 浼樺寲缂栬瘧鍣?19.43.34810 鐗?
          鐗堟潈鎵€鏈?C) Microsoft Corporation銆備繚鐣欐墍鏈夋潈鍒┿€?
          cl /c /I"E:\\Projects\\Code\\vcpkg\\installed\\x64-windows\\include" /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CHECK_FUNCTION_EXISTS=pthread_create /D "CMAKE_INTDIR=\\"Debug\\"" /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++20 /Fo"cmTC_76621.dir\\Debug\\\\" /Fd"cmTC_76621.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TP /errorReport:queue "E:\\Projects\\Code\\AI Agent\\database_v13\\build\\CMakeFiles\\CMakeScratch\\TryCompile-oxhjh9\\CheckFunctionExists.cxx"
          CheckFunctionExists.cxx
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"E:\\Projects\\Code\\AI Agent\\database_v13\\build\\CMakeFiles\\CMakeScratch\\TryCompile-oxhjh9\\Debug\\cmTC_76621.exe" /INCREMENTAL /ILK:"cmTC_76621.dir\\Debug\\cmTC_76621.ilk" /NOLOGO /LIBPATH:"E:\\Projects\\Code\\vcpkg\\installed\\x64-windows\\lib" /LIBPATH:"E:\\Projects\\Code\\vcpkg\\installed\\x64-windows\\lib\\manual-link" pthread.lib kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib "E:\\Projects\\Code\\vcpkg\\installed\\x64-windows\\lib\\*.lib" /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"E:/Projects/Code/AI Agent/database_v13/build/CMakeFiles/CMakeScratch/TryCompile-oxhjh9/Debug/cmTC_76621.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"E:/Projects/Code/AI Agent/database_v13/build/CMakeFiles/CMakeScratch/TryCompile-oxhjh9/Debug/cmTC_76621.lib" /MACHINE:X64  /machine:x64 cmTC_76621.dir\\Debug\\CheckFunctionExists.obj
        LINK : fatal error LNK1104: 鏃犳硶鎵撳紑鏂囦欢鈥減thread.lib鈥?[E:\\Projects\\Code\\AI Agent\\database_v13\\build\\CMakeFiles\\CMakeScratch\\TryCompile-oxhjh9\\cmTC_76621.vcxproj]
        宸插畬鎴愮敓鎴愰」鐩€淓:\\Projects\\Code\\AI Agent\\database_v13\\build\\CMakeFiles\\CMakeScratch\\TryCompile-oxhjh9\\cmTC_76621.vcxproj鈥?榛樿鐩爣)鐨勬搷浣?- 澶辫触銆?
        
        鐢熸垚澶辫触銆?
        
        鈥淓:\\Projects\\Code\\AI Agent\\database_v13\\build\\CMakeFiles\\CMakeScratch\\TryCompile-oxhjh9\\cmTC_76621.vcxproj鈥?榛樿鐩爣) (1) ->
        (Link 鐩爣) -> 
          LINK : fatal error LNK1104: 鏃犳硶鎵撳紑鏂囦欢鈥減thread.lib鈥?[E:\\Projects\\Code\\AI Agent\\database_v13\\build\\CMakeFiles\\CMakeScratch\\TryCompile-oxhjh9\\cmTC_76621.vcxproj]
        
            0 涓鍛?
            1 涓敊璇?
        
        宸茬敤鏃堕棿 00:00:00.36
        
      exitCode: 1
...
